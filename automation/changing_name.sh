jq ".name = \"$1\"" ./package.json > ./tmp.json && mv ./tmp.json ./package.json
jq ".name = \"$1\"" ./ionic.config.json > ./tmp.json && mv ./tmp.json ./ionic.config.json
sed -i "s/<string>olivery_appx<\/string>/<string>$1<\/string>/g" info.plist
sed -i "s/appName: .*,/appName: \"$1\",/" ./capacitor.config.ts
app_id=$2
#sed -i "s/appId: .*,/appId: \"mere.ram.${app_id}\",/" ./capacitor.config.ts
if [ "$app_id" = "unitedexp" ]; then
    # If $2 is "unitedexp", change the appId to ps.united.express.unitedexpress
    sed -i 's/appId: .*,/appId: "ps.united.express.unitedexpress",/' ./capacitor.config.ts
elif [ "$app_id" = "lightshipping" ]; then
        sed -i 's/appId: .*,/appId: "mere.ram.ligthshipping",/' ./capacitor.config.ts
else
    # For any other $2 value, use the appId mere.ram.${app_id}
    sed -i "s/appId: .*,/appId: \"mere.ram.${app_id}\",/" ./capacitor.config.ts
fi
