import requests
import json
import sys
import xml.etree.ElementTree as ET
import subprocess
from lxml import etree
def install_npm_package(package_name):
    try:
        # Run the npm install command
        result = subprocess.run(['npm', 'install', package_name], check=True, capture_output=True, text=True)
        # Print the output from the command
        print("Successfully installed:", package_name)
        print(result.stdout)  # To see any output from the npm command
    except subprocess.CalledProcessError as e:
        # Handle errors in the installation process
        print("Failed to install", package_name)
        print(e.output)
def handle_license_key(license_key, file_path):
    # Parse the XML file
    parser = etree.XMLParser(remove_blank_text=True)
    tree = etree.parse(file_path, parser)
    root = tree.getroot()

    # Namespace map for Android XML
    ns_map = {'android': 'http://schemas.android.com/apk/res/android'}

    # Find the application element to add the meta-data
    app = root.find("application")
    if app is not None:
        # Create a new meta-data element
        new_meta = etree.Element("meta-data", nsmap=ns_map)
        new_meta.set("{http://schemas.android.com/apk/res/android}name", "com.transistorsoft.locationmanager.license")
        new_meta.set("{http://schemas.android.com/apk/res/android}value", license_key)
        app.insert(0, new_meta)  # Insert at the beginning of the application element
    else:
        print("Application tag not found in the AndroidManifest.xml")

    # Create the new uses-permission element for USE_EXACT_ALARM
    #new_permission_alarm = etree.Element("uses-permission", nsmap=ns_map)
    #new_permission_alarm.set("{http://schemas.android.com/apk/res/android}name", "android.permission.USE_EXACT_ALARM")
    #new_permission_alarm.set("{http://schemas.android.com/apk/res/android}minSdkVersion", "34")
    for elem in root.findall(".//uses-permission", namespaces=ns_map):
        if elem.get("{http://schemas.android.com/apk/res/android}name") == "android.permission.SCHEDULE_EXACT_ALARM":
            # Add the minSdkVersion attribute
            elem.set("{http://schemas.android.com/apk/res/android}minSdkVersion", "34")
            break

    # Create the new uses-permission element for ACCESS_BACKGROUND_LOCATION
    new_permission_background = etree.Element("uses-permission", nsmap=ns_map)
    new_permission_background.set("{http://schemas.android.com/apk/res/android}name", "android.permission.ACCESS_BACKGROUND_LOCATION")
    new_permission_battery_optimization = etree.Element("uses-permission", nsmap=ns_map)
    new_permission_battery_optimization.set("{http://schemas.android.com/apk/res/android}name", "android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS")

    # Determine where to insert the new permissions
    last_permission = root.xpath("//uses-permission[last()]", namespaces=ns_map)
    if last_permission:
        insert_index = root.index(last_permission[0]) + 1
    else:
        insert_index = len(root)  # If no permissions, add at the end of the root

    # Insert the new permissions into the XML tree
    root.insert(insert_index, new_permission_background)
    root.insert(insert_index + 1, new_permission_background)  # Ensure order is maintained
    root.insert(insert_index, new_permission_battery_optimization)
    root.insert(insert_index + 1, new_permission_battery_optimization)
    # Save the updated XML back to the file
    tree.write(file_path, pretty_print=True, xml_declaration=True, encoding='utf-8')


def modify_gradle_script(original_file, modified_file):
    # Read the original Gradle file
    with open(original_file, 'r') as file:
        lines = file.readlines()

    # Define the lines to insert at the beginning
    insert_lines = [
        "Project background_geolocation = project(':transistorsoft-capacitor-background-geolocation')\n",
        "apply from: \"${background_geolocation.projectDir}/app.gradle\"\n"
    ]

    # Define the line to identify where to insert new lines
    insertion_point = "apply plugin: 'com.android.application'\n"

    # Find the index for the insertion point
    index = lines.index(insertion_point) + 1

    # Insert the lines right after the insertion point
    lines[index:index] = insert_lines

    # Add specific lines under the second 'release' block
    release_count = 0  # Counter to track the number of 'release' blocks encountered
    for i, line in enumerate(lines):
        if 'release {' in line.strip():
            release_count += 1
            if release_count == 1:  # Check if it's the second 'release' block
                indent = ' ' * (len(line) - len(line.lstrip()))
                lines.insert(i + 3, indent + '    proguardFiles "${background_geolocation.projectDir}/proguard-rules.pro"\n')
                break

    # Write to a new file or overwrite the original file
    with open(modified_file, 'w') as file:
        file.writelines(lines)
def add_entry_to_ext_block(original_file, modified_file):
    # Read the original Gradle file
    with open(original_file, 'r') as file:
        lines = file.readlines()

    # Define the new line to insert
    new_entry = "    playServicesLocationVersion = '21.0.1'\n"

    # Identify the 'ext {' block and insert the new entry
    for i, line in enumerate(lines):
        if line.strip() == 'ext {':
            # Insert the new line right after the opening of the ext block
            lines.insert(i + 1, new_entry)
            break

    # Write to a new file or overwrite the original file
    with open(modified_file, 'w') as file:
        file.writelines(lines)
def add_maven_repositories(original_file, modified_file):
    # Read the original Gradle file
    with open(original_file, 'r') as file:
        lines = file.readlines()

    # Define the new repository lines to add
    new_repositories = [
        "        maven { url(\"${project(':transistorsoft-capacitor-background-geolocation').projectDir}/libs\") }\n",
        "        maven { url 'https://developer.huawei.com/repo/' }\n",
        "        // capacitor-background-fetch\n",
        "        maven { url(\"${project(':transistorsoft-capacitor-background-fetch').projectDir}/libs\") }\n"
    ]

    # Find the line with 'mavenCentral()' and insert the new lines right after it
    inside_allprojects = False
    for i, line in enumerate(lines):
        if 'allprojects {' in line:
            # Insert the new lines after the 'mavenCentral()' line
            inside_allprojects = True
        elif inside_allprojects and 'mavenCentral()' in line:
            position = i + 1
            lines[position:position] = new_repositories
            break
        elif inside_allprojects and '}' in line:  # Detect the end of the 'allprojects' block
            inside_allprojects = False

    # Write to a new file or overwrite the original file
    with open(modified_file, 'w') as file:
        file.writelines(lines)

def post_request(website_db):
    url = 'https://master.olivery.app/olivery/customer/ondemand'

    headers = {
        'Content-Type': 'application/json',
        'Cookie': 'session_id=e47c8c6a45db0e20a824920e3e49421053cc35f4'
    }

    data = {
        "jsonrpc": "2.0",
        "params": {
            "website_db": website_db
        }
    }

    response = requests.post(url, headers=headers, json=data)
    return response.json()

def add_geo_plugins():
    command = ['node', 'add-geo-plugins.js']
    try:
        # Run the Node.js script
        subprocess.run(command, check=True)
        print("Added GEO Plugins successfully.")
    except subprocess.CalledProcessError as e:
        print(f"Error While Addeing GEO Plugins: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python script.py <website_db>")
        sys.exit(1)
    
    website_db = sys.argv[1]
    result = post_request(website_db)

    if result is not None:
        is_ondemand = result.get('result', {}).get('is_ondemand', False)
        license_key = result.get('result', {}).get('license_key', None)

        if is_ondemand and license_key:
            handle_license_key(license_key,'AndroidManifest.xml')
            add_geo_plugins()
            add_maven_repositories('android/build.gradle', 'android/build.gradle')
            add_entry_to_ext_block('./android/variables.gradle', './android/variables.gradle')
            modify_gradle_script('./android/app/build.gradle', './android/app/build.gradle')
            #install_npm_package('@transistorsoft/capacitor-background-fetch')
            #install_npm_package('@transistorsoft/capacitor-background-geolocation')
        else:
            print("Either not on-demand or missing license key.")
    else:
        print("Failed to parse JSON from the response or response was empty.")



