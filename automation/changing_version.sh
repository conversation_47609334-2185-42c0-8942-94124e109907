#!/bin/bash

#taking version from package.json
PACKAGE_JSON="./package.json"
BUILD_GRADLE="./android/app/build.gradle"

# Extract version from package.json
VERSION=$(grep '"version":' $PACKAGE_JSON | sed 's/.*"version": "\(.*\)".*/\1/')

# Extract major version number for versionCode
VERSION_CODE=${VERSION//./}

# Update versionName and versionCode in build.gradle
sed -i "s/versionName \".*\"/versionName \"${VERSION}\"/" $BUILD_GRADLE
sed -i "s/versionCode [0-9]*/versionCode ${VERSION_CODE}/" $BUILD_GRADLE

echo "Updated build.gradle with versionName: $VERSION and versionCode: $VERSION_CODE"
