#!/bin/bash
response=$(curl --location --request POST 'https://master.olivery.app/olivery/mobile/get_images' \
--header 'Content-Type: application/json' \
--header 'Cookie: session_id=e47c8c6a45db0e20a824920e3e49421053cc35f4' \
--data-raw "{
    \"jsonrpc\": \"2.0\",
    \"params\": {
        \"website_db\":\"$1\"
    }
}")
result_icon=$(echo "${response}" | jq ".result.icon")
result_splash=$(echo "${response}" | jq ".result.splash")
result_login=$(echo "${response}" | jq ".result.login")
result_gif_splash=$(echo "${response}" | jq ".result.gif_splash")
result_login_banner=$(echo "${response}" | jq ".result.login_banner")
if [ "$result_icon" != false ] && [ "$result_splash" != false ]; then
	echo "$response" | jq -r ".result.icon" > "./$1_icon.b64"
	echo "$response" | jq -r ".result.splash" > "./$1_splash.b64"
	base64 -D -i ./$1_icon.b64 -o ./resources/icon.png
	base64 -D -i ./$1_splash.b64 -o ./resources/splash.png
	rm -rf ./$1_icon.b64
	rm -rf ./$1_splash.b64
fi
if [ "$result_login" != false ]; then
        echo "$response" | jq -r ".result.login" > "./$1_login.b64"
        base64 -D -i ./$1_login.b64 -o ./resources/login.png
        rm -rf ./$1_login.b64
else
	rm -rf ./src/assets/login.png
fi
if [ "$result_gif_splash" != false ]; then
	echo "$response" | jq -r ".result.gif_splash" > "./$1_gif_splash.b64"
	base64 -D -i ./$1_gif_splash.b64 -o ./src/assets/splash.gif
	rm -rf ./$1_gif_splash.b64
fi

if [ "$result_login_banner" != false ]; then
        echo "$response" | jq -r ".result.login_banner" > "./$1_login_banner.b64"
        base64 -D -i ./$1_login_banner.b64 -o ./src/assets/banner.jpg
        rm -rf ./$1_login_banner.b64
fi
