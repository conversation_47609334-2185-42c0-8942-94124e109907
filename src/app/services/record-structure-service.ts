import { Injectable } from '@angular/core';
import { OdooJsonRPC } from './odooJsonRPC';
import { ObjectService } from './object-service';
import { TranslateService } from '@ngx-translate/core';
import * as resCompanySelectors from 'src/app/ngrx-store/res-company/store/selectors';
import { filter, take } from 'rxjs';
import { ResCompanyState } from 'src/app/ngrx-store/res-company/store';
import { Store } from '@ngrx/store';
import { HttpClient } from '@angular/common/http';


@Injectable()
export class RecordStructureService{
  recordStructures:any={}
  currencySymbol!:string
  many2manyMap:any={}
  constructor(
    private odooRpc:OdooJsonRPC,
    private objectService:ObjectService,
    private translate:TranslateService,
    private resCompany : Store<ResCompanyState>,
    private httpcall: HttpClient
  ){
    this.getCurrencySymbol().then(currencySymbol=>{
      this.currencySymbol=currencySymbol
    })
  }

  formatForGroup(group:any){
    let key = ''
    if (group.key) {
      key =this.translate.instant(group.key)
    } else {
      key =this.translate.instant('UNDEFINED')
    }
    let content = [
      [
        { 'value': key },
        { 'value': this.translate.instant('NUMBER_OF_RECORDS') + group.recordsLength }
      ]
    ]
    if (group.otherKeys) {
      for (let key of group.otherKeys) {
        content.push([
          {'value': key['name']},
          {'value': key['value']}
        ])
      }
    }
    return {
      'theme': 'olivery_theme',
      'header': {},
      'content': content,
      'footer': [
        {
          "icon": "chevron-down",
          "color": "primary",
          "callBackFunction": "toggleGroup"
        }
      ],
    } as RecordCardStructure;
  }

  formatRecordForCard(cardName:string,record:any){
    
    if(cardName && cardName in this.recordStructures){
      let structuredRecord = this.objectService.deepClone(this.recordStructures[cardName])
      if('header' in structuredRecord){
        
        if('header_bubles' in structuredRecord['header'] && structuredRecord['header']['header_bubles'].length > 0){
            for(let buble = 0; buble < structuredRecord['header']['header_bubles'].length ; buble++){
              structuredRecord['header']['header_bubles'][buble]['bubble_text']=this.getFieldValue(structuredRecord['header']['header_bubles'][buble]['bubble_text'],record,cardName)
              structuredRecord['header']['header_bubles'][buble]['secondary_color_field_name']=this.getFieldValue(structuredRecord['header']['header_bubles'][buble]['secondary_color_field_name'],record,cardName)
              structuredRecord['header']['header_bubles'][buble]['color_field_name']=this.getFieldValue(structuredRecord['header']['header_bubles'][buble]['color_field_name'],record,cardName)
            }
        }
        if('header_sub_bubles' in structuredRecord['header'] && structuredRecord['header']['header_sub_bubles'].length > 0){
          for(let buble = 0; buble < structuredRecord['header']['header_sub_bubles'].length ; buble++){
            structuredRecord['header']['header_sub_bubles'][buble]['bubble_text']=this.getFieldValue(structuredRecord['header']['header_sub_bubles'][buble]['bubble_text'],record,cardName)
            structuredRecord['header']['header_sub_bubles'][buble]['secondary_color_field_name']=this.getFieldValue(structuredRecord['header']['header_sub_bubles'][buble]['secondary_color_field_name'],record,cardName)
            structuredRecord['header']['header_sub_bubles'][buble]['color_field_name']=this.getFieldValue(structuredRecord['header']['header_sub_bubles'][buble]['color_field_name'],record,cardName)
          }
        }
        if('title' in structuredRecord['header']){
          for(let i=0;i<structuredRecord['header']['title'].length;i++){
            structuredRecord['header']['title'][i]['value']=this.getFieldValue(structuredRecord['header']['title'][i]['value'],record,cardName)
          }
        }
        if('sub_title' in structuredRecord['header']){
          for(let i=0;i<structuredRecord['header']['sub_title'].length;i++){
            structuredRecord['header']['sub_title'][i]['value']=this.getFieldValue(structuredRecord['header']['sub_title'][i]['value'],record,cardName)
          }
        }
      }
      if('content' in structuredRecord && structuredRecord['content'].length>0){
        for(let i=0;i<structuredRecord['content'].length;i++){
          for(let j=0;j<structuredRecord['content'][i].length;j++){

            if('name' in structuredRecord['content'][i][j]){
              structuredRecord['content'][i][j]['name']=this.getFieldValue(structuredRecord['content'][i][j]['name'],record,cardName)
            }
            if('value' in structuredRecord['content'][i][j] ){
              structuredRecord['content'][i][j]['value']=this.getFieldValue(structuredRecord['content'][i][j]['value'],record,cardName)
            }
            if('button' in structuredRecord['content'][i][j]){
              structuredRecord['content'][i][j]['button']['text']=this.getFieldValue(structuredRecord['content'][i][j]['button']['text'],record,cardName)
            }
            if('hyperlink' in structuredRecord['content'][i][j]){
              structuredRecord['content'][i][j]['hyperlink']=this.getFieldValue(structuredRecord['content'][i][j]['hyperlink'],record,cardName)
            }
            if('action_buttons' in structuredRecord['content'][i][j]) {
              for (let k = 0; k < structuredRecord['content'][i][j]['action_buttons'].length; k++) {
                if ('button' in structuredRecord['content'][i][j]['action_buttons'][k]) {
                  structuredRecord['content'][i][j]['action_buttons'][k]['button']['field_1_name'] = structuredRecord['content'][i][j]['action_buttons'][k]['button']['field_1']
                  structuredRecord['content'][i][j]['action_buttons'][k]['button']['field_2_name'] = structuredRecord['content'][i][j]['action_buttons'][k]['button']['field_2']
                  structuredRecord['content'][i][j]['action_buttons'][k]['button']['field_1'] = this.getFieldValue(structuredRecord['content'][i][j]['action_buttons'][k]['button']['field_1'], record, cardName)
                  structuredRecord['content'][i][j]['action_buttons'][k]['button']['field_2'] = this.getFieldValue(structuredRecord['content'][i][j]['action_buttons'][k]['button']['field_2'], record, cardName)
                }
              }
            }
            
          }
        }
      }

      if('side_content' in structuredRecord && structuredRecord['side_content'].length>0){
        for(let i=0;i<structuredRecord['side_content'].length;i++){
          for(let j=0;j<structuredRecord['side_content'][i].length;j++){

            if('name' in structuredRecord['side_content'][i][j]){
              structuredRecord['side_content'][i][j]['name']=this.getFieldValue(structuredRecord['side_content'][i][j]['name'],record,cardName)
            }
            if('value' in structuredRecord['side_content'][i][j] ){
              structuredRecord['side_content'][i][j]['value']=this.getFieldValue(structuredRecord['side_content'][i][j]['value'],record,cardName)
            }
            if('button' in structuredRecord['side_content'][i][j]){
              structuredRecord['side_content'][i][j]['button']['text']=this.getFieldValue(structuredRecord['side_content'][i][j]['button']['text'],record,cardName)
            }
            if('hyperlink' in structuredRecord['side_content'][i][j]){
              structuredRecord['side_content'][i][j]['hyperlink']=this.getFieldValue(structuredRecord['side_content'][i][j]['hyperlink'],record,cardName)
            }
            
          }
        }
      }

      if('notes_content' in structuredRecord && structuredRecord['notes_content'].length>0){
        for(let i=0;i<structuredRecord['notes_content'].length;i++){
          for(let j=0;j<structuredRecord['notes_content'][i].length;j++){

            if('name' in structuredRecord['notes_content'][i][j]){
              structuredRecord['notes_content'][i][j]['name']=this.getFieldValue(structuredRecord['notes_content'][i][j]['name'],record,cardName)
            }
            if('value' in structuredRecord['notes_content'][i][j] ){
              structuredRecord['notes_content'][i][j]['value']=this.getFieldValue(structuredRecord['notes_content'][i][j]['value'],record,cardName)
            }
            if('button' in structuredRecord['notes_content'][i][j]){
              structuredRecord['notes_content'][i][j]['button']['text']=this.getFieldValue(structuredRecord['notes_content'][i][j]['button']['text'],record,cardName)
            }
            if('hyperlink' in structuredRecord['notes_content'][i][j]){
              structuredRecord['notes_content'][i][j]['hyperlink']=this.getFieldValue(structuredRecord['notes_content'][i][j]['hyperlink'],record,cardName)
            }
            
          }
        }
      }

      return structuredRecord as RecordCardStructure
      
      
    }else{
      return false
    }
  }

  getFieldValue(text: string,record:any,cardName:string) {
    let fieldName=text
    if(fieldName && fieldName.includes('.length')){

      fieldName = fieldName.split('.length')[0]
      if(fieldName in record){
        return this.many2manyMap[cardName][fieldName].filter((rec:any)=> record[fieldName].includes(rec.id))
      }
      else{
        return fieldName
      }
    }
    else if(fieldName && fieldName.includes('[1]')){
      fieldName = fieldName.split('[1]')[0]
      if(fieldName in record){
        return record[fieldName][1] || "UNDEFINED"
      }
      else{
        return fieldName
      }
    }
    else if(fieldName && fieldName in record){
      
      let fieldType = this.getFieldType(fieldName,cardName)
      
      if(fieldType == 'selection'){
        return this.getSelectionItems(fieldName,cardName,record[fieldName])
      }
      else if(typeof record[fieldName] != 'boolean' || fieldType == 'boolean'){
        return record[fieldName].toString()
      }
      else{
        return record[fieldName]
      }
    }
    else{
      return fieldName
    }
  }

  getFieldType(fieldName: string, cardName: string): string | boolean {
    // Extract content and header from the record structure
    const { content, header } = this.recordStructures[cardName] || {};

    // Check the content fields for the matching field name
    if (Array.isArray(content)) {
        for (const contentField of content) {
            const fieldData = contentField[1];
            if (fieldData?.name === fieldName) {
                return fieldData.field_type || false;
            }
        }
    }

    // Check the header bubbles for the matching field name
    const headerBubbles = header?.header_bubles;
    if (Array.isArray(headerBubbles)) {
        for (const bubble of headerBubbles) {
            if (bubble?.bubble_text === fieldName) {
                return bubble.field_type || false;
            }
        }
    }

    const headerSubBubbles = header?.header_sub_bubles;
    if (Array.isArray(headerSubBubbles)) {
        for (const bubble of headerSubBubbles) {
            if (bubble?.bubble_text === fieldName) {
                return bubble.field_type || false;
            }
        }
    }

    // Return false if no matching field type is found
    return false;
  }

  getSelectionItems(fieldName: string, cardName: string, value: string): string {
    // Extract content and header from the record structure
    const { content, header } = this.recordStructures[cardName] || {};

    // Check the content fields for the matching field name and selection items
    if (Array.isArray(content)) {
        for (const contentField of content) {
            const fieldData = contentField[1];
            if (fieldData?.name === fieldName && Array.isArray(fieldData.selection_items)) {
                const selectedItem = fieldData.selection_items.find((item:any) => item[0] === value);
                return selectedItem ? selectedItem[1] : value;
            }
        }
    }

    // Check the header bubbles for the matching field name and selection items
    const headerBubbles = header?.header_bubles;
    if (Array.isArray(headerBubbles)) {
        for (const bubble of headerBubbles) {
            if (bubble?.bubble_text === fieldName && Array.isArray(bubble.selection_items)) {
                const selectedItem = bubble.selection_items.find((item:any) => item[0] === value);
                return selectedItem ? selectedItem[1] : value;
            }
        }
    }

    const headerSubBubbles = header?.header_sub_bubles;
    if (Array.isArray(headerSubBubbles)) {
        for (const bubble of headerSubBubbles) {
            if (bubble?.bubble_text === fieldName && Array.isArray(bubble.selection_items)) {
                const selectedItem = bubble.selection_items.find((item:any) => item[0] === value);
                return selectedItem ? selectedItem[1] : value;
            }
        }
    }
    // Return the original value if no match is found
    return value;
  }

  fetchStructure(cardName:string){
    if(cardName in this.recordStructures){
      return new Promise<any>(resolve => {
        resolve({...this.recordStructures[cardName],success:true});
      });
    }else{
      let self = this
      return this.odooRpc.call('rb_delivery.mobile_card_item','get_card',[cardName]).then(async cardStructureResponse=>{
        if(cardStructureResponse && cardStructureResponse.body && cardStructureResponse.body.result && cardStructureResponse.body.result.success){
          self.recordStructures[cardName]=cardStructureResponse.body.result.result
          let many2manyRelations = self.recordStructures[cardName].many2many_relations
          self.many2manyMap[cardName]={}
          for(let many2manyRelation in many2manyRelations){
            let recordsResponse = await self.odooRpc.searchRead(many2manyRelations[many2manyRelation],[],['id','display_name'],100,0,'id DESC')
            if(recordsResponse && recordsResponse.body && recordsResponse.body.result && recordsResponse.body.result.success){
              self.many2manyMap[cardName][many2manyRelation] = recordsResponse.body.result.result
            } 
            
          }
          return {success:true,neededFields:cardStructureResponse.body.result.result.fields_names}
        }
        else{
          return false
        }
      })
    }
    
  }

  async getCurrencySymbol(): Promise<string> {
    if (!this.currencySymbol) {
      
      const resCompanyInfo = await this.resCompany
        .select(resCompanySelectors.selectAllData)
        .pipe(filter((data) => data && data.length > 0), take(1))
        .toPromise();
      if (resCompanyInfo && resCompanyInfo.length > 0 && resCompanyInfo[0].currency_id) {
        const currencySelected = await this.odooRpc.searchRead('res.currency', [['id', '=', resCompanyInfo[0].currency_id[0]]], ['id', 'name', 'symbol'], 0, 0, 'id DESC');
  
        if (currencySelected.body.result && currencySelected.body.result.success) {
          return currencySelected.body.result.result[0].symbol;
        } else {
          return '';
        }
      } else {
        return '';
      }
    } else {
      return this.currencySymbol;
    }
  }

  async mapOrderDetails(order: any, response: any[]): Promise<any[]> {
    return Promise.all(
      response.map(async (detail) => {
        const newDetail = { ...detail };
        newDetail.cards = await Promise.all(
          newDetail.cards.map(async (card: any) => {
            const newCard = { ...card };
            newCard.card_items = await Promise.all(
              newCard.card_items.map(async (item: any) => {
                const newItem = { ...item };
                if (newItem.item_field && order[newItem.item_field] !== undefined) {
                  if (newItem.item_ttype == 'many2one') {
                    newItem.item_field = order[newItem.item_field]
                      ? order[newItem.item_field][1]
                      : order[newItem.item_field];
                  } else if (newItem.item_ttype == 'many2many') {
                    const descriptionTags = await this.odooRpc.searchRead(
                      newItem.inverse_model,
                      [['id', 'in', order[newItem.item_field]]],
                      ['id', 'display_name'],
                      0,
                      0,
                      'id DESC'
                    );
                    newItem.item_field = descriptionTags?.body.result.result
                      .map((tag: any) => tag.display_name)
                      .join(', ');
                  } else {
                    newItem.item_field = order[newItem.item_field];
                  }
                } else {
                  newItem.item_field = null;
                }
                return newItem;
              })
            );
            return newCard;
          })
        );
        return newDetail;
      })
    );
  }
  
  
  async getOrderDetailPageStructureInfo(record:any): Promise<any> {
    try {
      const response = await this.getOrderDetail();
      return await this.mapOrderDetails(record,response.body.result.result);
    } catch (error) {
      console.error('Error:', error);
      return null;
    }
  }

  public async getOrderDetail(): Promise<any> {
    const model = 'rb_delivery.mobile_order_detail'
    const method = 'get_mobile_order_detail_data'
    const params = {
      model,
      method,
    };
    return this.odooRpc.sendRequest('/api/call_method', params)
  }
}


export interface IGroup {
  key: string;
  type: string;
  recordsLength: number;
  keyId: number;
  structure:RecordCardStructure,
  domain:any[],
  fieldName:string,
  groupBy:string
}

export interface RecordCardStructure{
  header:any,
  content:any[],
  side_content:any[],
  notes_content:any[],
  footer:any[],
  id:number,
  fields:string[],
  state : string,
  theme:string,
  fields_names: string[]
}