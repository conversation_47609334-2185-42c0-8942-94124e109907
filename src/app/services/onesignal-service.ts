import { Injectable } from '@angular/core';
import OneSignal, { NotificationClickEvent, NotificationWillDisplayEvent, OSNotification, PushSubscriptionChangedState } from 'onesignal-cordova-plugin';
import * as userActions from 'src/app/ngrx-store/user/store/actions';
import * as userSelectors from 'src/app/ngrx-store/user/store/selectors';
import { UserState } from 'src/app/ngrx-store/user/store';
import { Store } from '@ngrx/store';
import { filter, take } from 'rxjs';
import { OdooJsonRPC } from './odooJsonRPC';
import { LoadingController, ModalController, NavController, Platform, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import AppEntryPoint from "onesignal-cordova-plugin"
import { OpenTimerService } from './openTimerServices';
import { Router } from '@angular/router';
import { ErrorHandlerService } from './error-handler-service';
import { NgZone } from '@angular/core';
import { AnnouncementCardComponent } from '../components/announcement-card/announcement-card.component';




@Injectable()
export class OnesignalService {
    window:any
    appId:string|undefined=undefined
    currentPlayerId:string|undefined|null=undefined
    subscriptionChangeListener: (pushSubscriptionChangedState: PushSubscriptionChangedState) => void;
    notificationPermissionListener: (notificationPermissionEvent: boolean) => Promise<void>;
    notificationClickListener: (notificationClickEvent: NotificationClickEvent) => Promise<void>;
    notificationReceivedListener: (notificationWillDisplayEvent: NotificationWillDisplayEvent) => Promise<void>;
    savedPlayerId: string | null | undefined;

    private displayedNotificationIds: Set<string> = new Set<string>();

    listenersAdded!: boolean;
    constructor(
        private userState: Store<UserState>,
        private odooJsonRPC: OdooJsonRPC,
        private toastCtrl:ToastController,
        public platform: Platform,
        private modalCtrl : ModalController,
        private translate : TranslateService,
        private navCtrl:NavController,
        private openTimerServices : OpenTimerService,
        private router:Router,
        private loadingCtrl:LoadingController,
        private errorService:ErrorHandlerService,
        private zone: NgZone
    ) {
        let self=this
        this. subscriptionChangeListener = function (pushSubscriptionChangedState: PushSubscriptionChangedState) {
            if(pushSubscriptionChangedState.current.id != pushSubscriptionChangedState.previous.id){
                self.updatePlayerId()
            }
        };

        this.notificationPermissionListener = async function (notificationPermissionEvent:boolean) {
            let isAndroid = this.platform.is('android')
            let hasPermission = await OneSignal.Notifications.hasPermission()
            if(hasPermission){
                self.oneSignalOptIn()
                
            }else if (isAndroid){
                OneSignal.Notifications.canRequestPermission().then(canRequestPermission=>{
                    if(canRequestPermission){
                        OneSignal.Notifications.requestPermission(true).then(accepted => {
                            if(accepted){
                                self.oneSignalInit()
                                self.oneSignalOptIn()
                            }
                            else{
                            }
                        })
                    }

                })
            }

        };

        this.notificationClickListener = async function (notificationClickEvent: NotificationClickEvent) {
            self.onnotificationClickListener(notificationClickEvent.notification,true)
            
        };

        this.notificationReceivedListener = async function (notificationWillDisplayEvent: NotificationWillDisplayEvent) {
            
            let notification: OSNotification = notificationWillDisplayEvent.getNotification();
      
            if (self.displayedNotificationIds.has(notification.notificationId)) {
              notificationWillDisplayEvent.preventDefault();
              return;
            }
      
            self.displayedNotificationIds.add(notification.notificationId);
      
            notificationWillDisplayEvent.preventDefault();
      
            self.checkNotificationType(notification, false);
          };
      
    }


    removeListeners() {
        let isMobile = this.checkisMobile()
        if(!isMobile){
            return
        }
        OneSignal.User.pushSubscription.removeEventListener("change", this.subscriptionChangeListener)
        OneSignal.Notifications.removeEventListener("click", this.notificationClickListener);
        OneSignal.Notifications.removeEventListener("foregroundWillDisplay", this.notificationReceivedListener);
        OneSignal.Notifications.removeEventListener("permissionChange", this.notificationPermissionListener);
        
      }

    async oneSignalInit(): Promise<void> {
        let isMobile = this.checkisMobile()
        this.savedPlayerId = sessionStorage.getItem('playerId');
        if(!isMobile){
            return
        }
        if(this.savedPlayerId){
            this.updatePlayerId()
            return
        }
        if(!isMobile){
            return
        }
        
        OneSignal.Notifications._setPropertyAndObserver()
        
        if(this.appId ){
            OneSignal.Notifications._setPropertyAndObserver()
            await AppEntryPoint.initialize(this.appId)
            // Prompts the user for notification permissions.
            this.waitForPermissionChange()
            
        }
        else{
            this.loadOneSignalConfiguration()
        }
        
        

    }
    waitForPermissionChange() {
        

        
        
        setTimeout(()=>{
            this.notificationPermissionListener(true)
        },1000)
        
    }
    loadOneSignalConfiguration(){
        this.odooJsonRPC.searchRead('rb_delivery.one_signal', [], ['app_id'], 1, null, "").then(data => {
            this.appId=data.body.result.result[0].app_id
            if(this.appId){
                this.oneSignalInit()
            }
            
        }).catch(error=>{
            this.oneSignalOptOut()
        })
    }

    async oneSignalOptOut(): Promise<void> {
        let isMobile = this.checkisMobile()    
        if(!isMobile || !OneSignal || !OneSignal.User || !OneSignal.User.pushSubscription){
            return
        }
        this.currentPlayerId=undefined
        
        try {
            const isOptedIn = await OneSignal.User.pushSubscription.getOptedInAsync();            
            if (isOptedIn) {
                let notificationClickRemovedListener = async function (event: any) {
                    
                };
                OneSignal.Notifications.removeEventListener("click", notificationClickRemovedListener);
                OneSignal.User.pushSubscription.optOut()
                this.userState.select(userSelectors.selectUserInfo).pipe(filter(userInfo=>!!userInfo),take(1)).subscribe(userInfo => {
                    this.userState.dispatch(new userActions.UpdateHttp({id:userInfo.id, values:{'player_id':''} }))

                })
            }else{
                OneSignal.User.pushSubscription.optOut()
            }
        } catch (error) {
            console.error('getOptedInAsync failed:', error);
        }
    }
    checkisMobile() {
        if(!this.platform.is('capacitor')){
            return false
        }
        return true
    }

    oneSignalOptIn(){
        let self=this
        let isMobile = this.checkisMobile()
        if(!isMobile){
            return
        }
        this.updatePlayerId()
    }
    async updatePlayerId(loadingToast:HTMLIonLoadingElement | undefined = undefined,counter=0) {
        if(loadingToast)
            loadingToast.dismiss()

        let playerId= await OneSignal.User.pushSubscription.getIdAsync() || this.savedPlayerId || ''
        let oneSignalId = await OneSignal.User.getOnesignalId()
        if(!playerId || !oneSignalId){
            if (counter>=2){
                this.errorService.presentError(this.translate.instant('ERROR_WITH_ONE_SIGNAL'),this.translate.instant('ONE_SIGNAL_NOT_CONFIGURED_PROPERLY'))
            }else{
                setTimeout(()=>{
                    counter+=1
                    this.updatePlayerId(loadingToast,counter)
                },5000)
            }
            
            
        }else{
            this.userState.select(userSelectors.selectUserInfo).pipe(filter(userInfo=>!!userInfo),take(1)).subscribe(async userInfo => {
                this.currentPlayerId = playerId
                this.userState.dispatch(new userActions.UpdateHttp({id:userInfo.id, values:{'player_id':playerId} }))
                sessionStorage.setItem('playerId', playerId);
                OneSignal.User.pushSubscription.optIn()
                if(loadingToast)
                   loadingToast.dismiss()
            })
        }
        
    }
    addAllListeners() {
        let isMobile = this.checkisMobile()
        if(!isMobile  || this.listenersAdded){
            return
        }
        this.listenersAdded=true
        OneSignal.User.pushSubscription.addEventListener("change", this.subscriptionChangeListener)
        OneSignal.Notifications.addEventListener("click", this.notificationClickListener);
        OneSignal.Notifications.addEventListener("foregroundWillDisplay", this.notificationReceivedListener);
        OneSignal.Notifications.addEventListener("permissionChange", this.notificationPermissionListener);
    }



    async goToOrder(notification:OSNotification){
        let data = notification.additionalData
        let action:string | undefined=undefined
        let domain: any[]=[]
        if(data && 'type' in data && data.type == 'chat_notification'){ 
            action='chatOrder'
        }
        if(data && 'type' in data && data.type == 'notification_timer' && 'notification_timer' in data && data['notification_timer']){
            return
        }
        if(data && 'model' in data && data.model == 'rb_delivery.order'  && 'sequence' in data && data['sequence']){
            domain = [['sequence','=',data['sequence']]]
        }
        else if(data && 'order_sequence' in data && data['order_sequence']){
            domain = [['sequence','=',data['order_sequence']]]
        }
        if(domain.length == 0){
            this.router.navigate(['/tabs/dashboard'])
            return
        }
        else{
            let queryParams = {
                queryParams: {
                    domain: JSON.stringify(domain),
                    action:action
                }
            };
            this.zone.run(() => { 
                this.router.navigate(['/tabs/orders'], queryParams);
              });
            
        }
        
    }

    checkNotificationType(notification: OSNotification|undefined,notificationClicked:boolean) {
        let data = notification?.additionalData
        console.log(notification)
        let forceAssign = false
        if (notification?.groupKey == "ForceNearestDriver") {
            forceAssign = true
        }
        if(data && 'type' in data && data.type == 'notification_timer' && 'notification_timer' in data && data['notification_timer']&&'sequence' in data) {
            let domainSearch:any = [['sequence','=',data['sequence']]]
            if (data['sequence']) {
                const seq = Number(data['sequence']);
                if (!isNaN(seq) && Number.isInteger(seq) && seq > 0 && seq <= 2147483647) {
                  domainSearch = ['|', ['sequence', '=', seq], ['id', '=', seq]];
                }
              }
            this.openTimerServices.openAlertTimer(domainSearch,forceAssign)  
        }
        else if (data && 'announcement_id' in data && data['announcement_id']){
            this.showAnnouncementPopup(data)
        }
        else if(notification && !notificationClicked){
            this.showNotificationToast(notification)
        }
    }

    async showAnnouncementPopup(data:any){
       let announcement = await this.fetchAnnouncement(data)
       this.modalCtrl.create({
             component: AnnouncementCardComponent,
             mode:'ios',
             cssClass:"announcement-dialog",
             componentProps: {
               announcement: announcement
               
             }
           }).then(modal =>{
             modal.present()
           })
    }

    async fetchAnnouncement(announcement:any) {
        let announcements = await this.odooJsonRPC.searchRead('rb_delivery.announcement', [['id', '=', announcement.announcement_id]], ['title', 'description', 'images', 'link'], 0, 0, 'create_date desc')
        if (announcements.body && announcements.body.result && announcements.body.result.result) {
          return announcements.body.result.result[0]
        }
     }

    showNotificationToast(notification: OSNotification) {
        this.toastCtrl.create({
            header:notification.title,
            message:notification.body,
            mode:"ios",
            duration:3000,
            position:"top",
            buttons:[{
                text:this.translate.instant('GO'),
                handler:()=>{
                    this.goToOrder(notification)
                }
            }]

        }).then(toast=>{
            toast.present()
        })
    }
    onnotificationClickListener(notification: OSNotification,notificationClicked:boolean) {
        this.checkNotificationType(notification,notificationClicked)
        this.goToOrder(notification)
        
            
    }

    reloadApp() {
        this.removeListeners()
        window.location.reload() 
    }



}