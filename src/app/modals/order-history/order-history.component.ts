import { Component, OnInit,Input } from '@angular/core';
import { Subscription, fromEvent  , take} from 'rxjs';
import { ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Store } from '@ngrx/store';
import { filter } from 'rxjs';

import { ClientConfigurationState } from 'src/app/ngrx-store/client-configuration/store/state';
import * as clientConfigurationsSelector from '../../ngrx-store/client-configuration/store/selectors';

@Component({
  selector: 'order-history',
  templateUrl: './order-history.component.html',
  styleUrls: ['./order-history.component.scss'],
})
export class OrderHistoryComponent implements OnInit {

  @Input('items') items:any[] = [];
  @Input() order:any;
  userInfo : any;
  clientConfigurations: any = {}

  language: any
  constructor(
    private modalCtrl: ModalController,
    private clientConfigurationStore: Store<ClientConfigurationState>,

    ) { }
  lastItem :any
  ngOnInit() {
    if(this.items.length > 0){
      let length = this.items.length - 1
      this.lastItem = this.items[length] 
    }
    this.getClientConfiguration();
  }
  back() {
    this.modalCtrl.dismiss();

  }
  formatDate(date:any){
    return moment.utc(date).local().format('DD/MM/YYYY HH:mm');
  }

    getClientConfiguration(){
        this.clientConfigurationStore.select(clientConfigurationsSelector.selectClientConfigurationByKeys(['show_log_creator_name'])).pipe(filter((keys) => keys && keys.length > 0), take(1)).subscribe(configurationKeys => {
          if (configurationKeys && configurationKeys.length > 0) {
            for (let configurationKey of configurationKeys) {
                this.clientConfigurations[configurationKey.key] = configurationKey.value
            }            
          }
        })
      }

}




