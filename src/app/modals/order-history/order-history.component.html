<ion-header mode="ios" [translucent]="true">
  <ion-toolbar mode="ios">
    <ion-buttons slot="start">
      <ion-button color="dark" (click)="back()">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title style="text-align: center">
      <span>{{ "ORDER_HISTORY" | translate }}</span>
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-card mode="ios" class="timeline-main-card">
    <ion-item lines="full">
      <ion-label
        *ngIf="order.create_date"
        style="
          color: #000;
          font-size: 1.1rem;
          text-align: start;
        "
      >
      {{ formatDate(order.create_date) }}
      </ion-label>
    </ion-item>
    <ion-row style="margin: 10px; text-align: center">
      <div style="height: 50px; text-align: center">
        <ion-label
          style="color: #000; font-size: 1.4rem;"
        >
          <span style="margin: 2px">
            {{ "ORDER" | translate }}
          </span>
          <span style="margin: 2px">
            {{ order.sequence }}
          </span>
        </ion-label>
      </div>
    </ion-row>
    <ion-row *ngFor="let item of items">
      <ion-col size="12">
        <div>
          <ion-item lines="none" style="display: flex; flex-wrap: wrap; align-items: center;">
            <ion-icon slot="start"
              src="../../../assets/icon/donestate.svg"
              style="font-size: 1.2rem; margin-right: 5px;"
            ></ion-icon>
            
            <ion-label 
              style="
                color: #000;
                font-size: 1rem;
                margin: 5px;
                flex: 1 1 50%;
                min-width: 100px;
                box-sizing: border-box;
              "
            >
              {{ formatDate(item.creation_date) }}
            </ion-label>
            
            <ion-label slot="end"
              style="
                color: #000;
                font-size: 1rem;
                margin: 5px;
                flex: 1 1 25%;
                min-width: 100px;
                text-align: end;
                box-sizing: border-box;
              "
            >
            <span style="display: block;font-weight: bold;"  *ngIf="item && item.create_uid && item.create_uid[1] && (item.is_message || clientConfigurations && clientConfigurations.show_log_creator_name )
            ">{{item.create_uid[1]}}</span>              
              {{ item.new_state }}
            </ion-label>
          </ion-item>          
          <div
            style="margin: 5px; display: flex; align-items: center"
            *ngIf="lastItem != item"
          >
            <ion-icon
              src="../../../assets/icon/union.svg"
              style="font-size: 1.5rem; margin: 5px;margin-inline-start: 8px;"
            ></ion-icon>
            <ion-item
              style="
                border-bottom: 1px solid grey;
                height: 1px;
                margin: 5px;
                position: absolute;
                left: 30px;
                right: 30px;
              "
            ></ion-item>
          </div>
        </div>
      </ion-col>
    </ion-row>
  </ion-card>
</ion-content>
