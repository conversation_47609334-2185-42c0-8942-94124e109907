import { Component, ElementRef, Input, OnInit, OnDestroy, ViewChild } from '@angular/core';
import { ModalController, Platform } from '@ionic/angular';
import { Haptics, ImpactStyle } from '@capacitor/haptics';
import { Storage } from '@ionic/storage-angular';
import BarcodeScanner from 'barcode-scanner';
import { ViewDidEnter } from '@ionic/angular';
import { captureFrame, drawBoundingBox, getCameraStream, toggleTorch } from '../barcode-scan/camera-frame-utils';
import { BehaviorSubject, Observable } from 'rxjs';

@Component({
  selector: 'folllow-orders-scanner',
  templateUrl: './folllow-orders-scanner.component.html',
  styleUrls: ['./folllow-orders-scanner.component.scss'],
})

export class FollowOrdersScanner  implements OnInit, OnDestroy, ViewDidEnter {
  @Input({required:true}) mode!: 'single' | 'multi';
  @ViewChild('reader') reader!: ElementRef | undefined;
  @ViewChild('barcodeList', { static: false }) barcodeList!: ElementRef;
  barcodes: string[] = [];
  @Input() showCompleteWithoutBarcodeButton: boolean = false;
  private flashSubject = new BehaviorSubject<boolean>(false);
  flash$ = this.flashSubject.asObservable();

  get flash(): boolean {
    return this.flashSubject.value;
  }

  set flash(value: boolean) {
    this.flashSubject.next(value);
  }
  userInfo: any;
  selectedType: any;
  scannerLoading: boolean = true;
  isScanning: boolean = false;
  @Input() modeName: 'barcode' | 'qrcode' = 'barcode';
  latestResult: string | null = null;

  typeSelectionItems: string[] = [];

  videoElement?: HTMLVideoElement;
  canvasElement?: HTMLCanvasElement;
  frameInterval?: any;
  scanRegionBox?: { x: number; y: number; width: number; height: number };


  constructor(
    private modalCtrl: ModalController,
    private storage: Storage,
    private platform: Platform,
  ) {}

  ngAfterViewInit(): void {}

  ngOnInit() {}

  ionViewDidEnter() {
    if (this.typeSelectionItems.length > 0 && !this.selectedType) {
      this.selectedType = this.typeSelectionItems[0];
    }
    this.getUserInfo();
    this.startScanner();
    if(this.platform.is('ios')) {
      this.setupCameraElementsIos()
    } else{ 
      this.setupCameraElementsAndroid();
    }
  }

  async setupCameraElementsAndroid() {
    const container = document.getElementById('scanner-container');
    if (!container) return;
    // Only create video/canvas if not already present
    if (!this.videoElement) {
      this.videoElement = document.createElement('video');
      this.videoElement.setAttribute('playsinline', 'true');
      this.videoElement.style.width = '100%';
      this.videoElement.style.height = '100%';
      this.videoElement.style.objectFit = 'cover';
      this.videoElement.style.position = 'absolute';
      this.videoElement.style.top = '0';
      this.videoElement.style.left = '0';
      this.videoElement.style.zIndex = '1';
      this.videoElement.style.transform = '';
      container.appendChild(this.videoElement);
    }
    // Only create canvas if not already present
    if (!this.canvasElement) {
      let canvasElement = document.getElementById('scan-region') as HTMLCanvasElement;
      if (!canvasElement) {
        canvasElement = document.createElement('canvas');
        canvasElement.id = 'scan-region';
      }
      canvasElement.style.width = '95%';
      canvasElement.style.height = '20%';
      canvasElement.style.position = 'absolute';
      canvasElement.style.top = '40%';
      canvasElement.style.left = '2.5%';
      canvasElement.style.border = '2px solid rgba(0, 255, 0, 0.5)';
      canvasElement.style.borderRadius = '10px';
      canvasElement.style.backgroundColor = 'transparent'; // Always transparent
      canvasElement.style.pointerEvents = 'none';
      canvasElement.style.zIndex = '-99999';
      this.canvasElement = canvasElement;
      container.appendChild(canvasElement);
    }
  }

  async setupCameraElementsIos() {
    const container = document.getElementById('scanner-container');
    if (!container) return;
    if (!this.videoElement) {
      this.videoElement = document.createElement('video');
      this.videoElement.setAttribute('playsinline', 'true');
      this.videoElement.style.width = '100%';
      this.videoElement.style.height = '100%';
      this.videoElement.style.objectFit = 'cover';
      this.videoElement.style.position = 'absolute';
      this.videoElement.style.top = '0';
      this.videoElement.style.left = '0';
      this.videoElement.style.zIndex = '1';
      this.videoElement.style.transform = '';
      container.appendChild(this.videoElement);
    }
    if (!this.canvasElement) {
      let canvasElement = document.getElementById('scan-region') as HTMLCanvasElement;
      if (!canvasElement) {
        canvasElement = document.createElement('canvas');
        canvasElement.id = 'scan-region';
      }
      canvasElement.style.width = '95%';
      canvasElement.style.height = '20%';
      canvasElement.style.position = 'absolute';
      canvasElement.style.top = '40%';
      canvasElement.style.left = '2.5%';
      canvasElement.style.border = '2px solid rgba(0, 255, 0, 0.5)';
      canvasElement.style.borderRadius = '10px';
      canvasElement.style.backgroundColor = 'transparent';
      canvasElement.style.pointerEvents = 'none';
      canvasElement.style.zIndex = '-99999';
      this.canvasElement = canvasElement;
      container.appendChild(canvasElement);
    }
  }

  getUserInfo() {
    this.storage.get('user_info').then((userInfo) => {
      if (userInfo) {
        this.userInfo = userInfo;
      }
    });
  }

  playSound() {
    const audio = new Audio('/assets/audio/beep.mp3');
    audio.play();
  }

  triggerVibration() {
    Haptics.impact({
      style: ImpactStyle.Heavy,
    });
  }

  async startScanner() {
    if (!this.videoElement || !this.canvasElement) {
      if(this.platform.is('ios')) {
        await this.setupCameraElementsIos()
      } else{ 
        await this.setupCameraElementsAndroid();
      }
    }
    try {
      // Start camera with torch enabled automatically
      const stream = await getCameraStream(this.videoElement!, true);
      this.videoElement!.srcObject = stream;
      await this.videoElement!.play();
      this.isScanning = true;
      this.scannerLoading = false;
      this.flash = true; // Update UI to show flash is on
      this.startFrameCapture();
    } catch (err) {
      console.error('Camera error:', err);
      this.scannerLoading = false;
    }
  }

  startFrameCapture() {
    if (this.frameInterval) clearInterval(this.frameInterval);
    this.frameInterval = setInterval(() => this.processFrame(), 300);
  }

  async processFrame() {
    if (!this.videoElement || !this.canvasElement || !this.isScanning) return;
    const base64 = captureFrame(this.videoElement, this.canvasElement, this.modeName, this.platform.is('ios'));
    if (!base64) return;
    try {
      const scanResult = await BarcodeScanner.scanFromImage({ base64, path: base64 });
      if (scanResult.hasContent && scanResult.content) {
        this.latestResult = scanResult.content;
        if (scanResult.boundingBox) {
          this.scanRegionBox = scanResult.boundingBox;
          drawBoundingBox(this.canvasElement, scanResult.boundingBox);
        }
        if (!this.barcodes.includes(scanResult.content)) {
          this.barcodes.push(scanResult.content);
          if (this.mode === 'single') {
            this.stopScanner();
          } else {
            this.triggerVibration();
            this.triggerFlashEffect();
          }
        }
      } else {
        this.scanRegionBox = undefined;
        this.clearCanvas();
      }
    } catch (err) {
      // Ignore frame errors
    }
  }

  clearCanvas() {
    if (!this.canvasElement) return;
    const ctx = this.canvasElement.getContext('2d');
    if (ctx) ctx.clearRect(0, 0, this.canvasElement.width, this.canvasElement.height);
  }

  onScannFail(error: string) {
    console.error(error)
  }

  async stopScanner() {
    this.isScanning = false;
    if (this.frameInterval) clearInterval(this.frameInterval);
    if (this.videoElement && this.videoElement.srcObject) {
      // Turn off torch before stopping the camera
      try {
        await toggleTorch(this.videoElement.srcObject as MediaStream, false);
      } catch (error) {
        console.warn('Failed to turn off torch:', error);
      }
      const tracks = (this.videoElement.srcObject as MediaStream).getTracks();
      tracks.forEach(track => track.stop());
      this.videoElement.srcObject = null;
    }
    this.flash = false; // Update UI to show flash is off
    this.clearCanvas();
    
    if (this.barcodes.length === 0) {
      this.modalCtrl.dismiss({ scanningFinished: true });
      return;
    }

    this.modalCtrl.dismiss(this.barcodes[0])
  }
  triggerFlashEffect() {
    this.flash = true;
    setTimeout(() => {
      this.flash = false;
    }, 300);
  }

  async toggleFlashlight() {
    if (this.videoElement && this.videoElement.srcObject) {
      try {
        const newFlashState = !this.flash;
        const success = await toggleTorch(this.videoElement.srcObject as MediaStream, newFlashState);
        if (success) {
          this.flash = newFlashState;
        }
      } catch (error) {
        console.error('Failed to toggle flashlight:', error);
      }
    }
  }

  async completeWithoutBarcode() {
    if (this.isScanning) {
      try {
        this.isScanning = false;
        if (this.frameInterval) clearInterval(this.frameInterval);
        if (this.videoElement && this.videoElement.srcObject) {
          const tracks = (this.videoElement.srcObject as MediaStream).getTracks();
          tracks.forEach(track => track.stop());
          this.videoElement.srcObject = null;
        }
        this.clearCanvas();
        this.modalCtrl.dismiss('complete');
      } catch (err) {
        console.error('Error completing without barcode:', err);
      }
    }
    this.modalCtrl.dismiss('complete');
  }


  async changeMode(event: any) {
    const newMode = event && event.detail && event.detail.value ? event.detail.value : this.modeName;
    if (newMode === this.modeName) return;
    this.modeName = newMode;
    if (this.canvasElement) {
      if (this.modeName === 'barcode') {
        this.canvasElement.style.width = '95%';
        this.canvasElement.style.height = '20%';
        this.canvasElement.style.top = '40%';
        this.canvasElement.style.left = '2.5%';
      } else if (this.modeName === 'qrcode') {
        this.canvasElement.style.width = '80%';
        this.canvasElement.style.height = '80%';
        this.canvasElement.style.top = '10%';
        this.canvasElement.style.left = '10%';
      }
    }
    if (this.isScanning) {
      await this.stopScanner();
      this.startScanner();
    }
  }

  ngOnDestroy(): void {
    // Complete the flashlight subject to prevent memory leaks
    this.flashSubject.complete();
  }
}