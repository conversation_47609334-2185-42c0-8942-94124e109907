<ion-content class="ion-padding">
  <div class="scanner-container" style="position:relative;">
    <div  [ngClass]="{ 'scan-overlay.barcode': modeName === 'barcode', 'scan-overlay.qrcode': modeName === 'qrcode' }"></div>

    <!-- Flashlight toggle button - top left corner -->
    <ion-button
      *ngIf="isScanning"
      (click)="toggleFlashlight()"
      fill="clear"
      class="flashlight-btn"
      [class.flashlight-on]="flash$ | async">
      <ion-icon name="flashlight" slot="icon-only"></ion-icon>
    </ion-button>

    <div id="scanner-container" class="video-container" [ngClass]="modeName"></div>

    <div class="scan-overlay" [ngClass]="modeName">
      <canvas id="scan-region" class="scan-region"></canvas>
    </div>
    

    <div class="result-container" [ngStyle]="{'background':errorMessage?'rgba(250, 0, 0, 0.7)':'rgba(0, 0, 0, 0.7)'}">
      <div class="result-box" *ngIf="errorMessage">
        <h3>{{ 'ERROR:' | translate }}</h3>
        <p class="result-value">{{ errorMessage | translate }}</p>
      </div>
      <div class="result-box" *ngIf="latestResult">
        <h3>{{ 'SCANNED_CODE' | translate }}</h3>
        <p class="result-value">{{ latestResult }}</p>
      </div>
    </div>
  </div>

  <div class="controls">
    <ion-segment [(ngModel)]="modeName" (ionChange)="changeMode($event)">
      <ion-segment-button value="barcode">
        <ion-label>{{ 'BARCODE' | translate }}</ion-label>
      </ion-segment-button>
      <ion-segment-button value="qrcode">
        <ion-label>{{ 'QRCODE' | translate }}</ion-label>
      </ion-segment-button>
    </ion-segment>
    
    <ion-button expand="block" (click)="isScanning ? stopScanner() : startScanner()" [color]="isScanning ? 'danger' : 'primary'">
        {{ isScanning ? ('STOP_SCAN' | translate) : ('START_SCAN' | translate) }}
    </ion-button>

    <ion-button expand="block" (click)="completeWithoutBarcode()" *ngIf="showCompleteWithoutBarcodeButton">
        {{ 'COMPLETE_WITHOUT_BARCODE' | translate }}
    </ion-button>
    <ion-button expand="block" (click)="addManualBarcode()">
        {{ 'ADD_MANUAL_bARCODE' | translate }}
    </ion-button>
  
  </div>

  <!-- Scan history -->
  <div class="scan-history" *ngIf="barcodes.length > 0">
    <h3>{{ 'SCAN_HISTORY' | translate }}</h3>
    <ion-list>
      <ion-item *ngFor="let barcode of barcodes; let i = index">
          {{ i + 1 }}.
          <ng-container (click)="openRecordCard(orders[barcode])">
            <ion-label style="display: flex;
                            padding-inline-start: 15px;">
                <ion-img style="width: 30px;padding-inline: 4px;" src="../../../assets/icon/multi_scanner_sequence.svg"></ion-img>
                {{ barcode }}
            </ion-label>
            <ion-label style="display: flex;
                        padding-inline-start: 15px;">
                <ion-img style="width: 30px;padding-inline: 4px;" src="../../../assets/icon/multi_scanner_areas.svg"></ion-img>
                <ion-col *ngIf="!orders.hasOwnProperty(barcode) && !orders[barcode]">
                    <ion-spinner duration="3" style="zoom: .5;"/>
                </ion-col>
                {{ orders[barcode].customer_area[1] }}
            </ion-label>
            <ion-icon (click)="openRecordCard(orders[barcode])" src="/assets/icon/openLink.svg" style="font-size: x-large;padding: 5px;"></ion-icon>
        </ng-container>
      </ion-item>
    </ion-list>
  </div>
</ion-content>
