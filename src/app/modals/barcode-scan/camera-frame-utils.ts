// Utility functions for camera frame capture and canvas drawing
export async function getCameraStream(videoElement: HTMLVideoElement, enableTorch: boolean = false): Promise<MediaStream> {
  const constraints: MediaStreamConstraints = {
    video: {
      facingMode: 'environment',
      width: { ideal: 1280 },
      height: { ideal: 720 }
    }
  };

  const stream = await navigator.mediaDevices.getUserMedia(constraints);

  // Try to enable torch if requested and supported
  if (enableTorch) {
    try {
      const track = stream.getVideoTracks()[0];
      const capabilities = track.getCapabilities();

      if ('torch' in capabilities) {
        await track.applyConstraints({
          advanced: [{ torch: true } as any]
        });
        console.log('Torch enabled successfully');
      } else {
        console.log('Torch not supported on this device');
      }
    } catch (error) {
      console.warn('Failed to enable torch:', error);
    }
  }

  return stream;
}

// Function to toggle torch on existing stream
export async function toggleTorch(stream: MediaStream, enable: boolean): Promise<boolean> {
  try {
    const track = stream.getVideoTracks()[0];
    const capabilities = track.getCapabilities();

    if ('torch' in capabilities) {
      await track.applyConstraints({
        advanced: [{ torch: enable } as any]
      });
      console.log(`Torch ${enable ? 'enabled' : 'disabled'} successfully`);
      return true;
    } else {
      console.log('Torch not supported on this device');
      return false;
    }
  } catch (error) {
    console.warn(`Failed to ${enable ? 'enable' : 'disable'} torch:`, error);
    return false;
  }
}

export function captureFrame(
  video: HTMLVideoElement,
  canvas: HTMLCanvasElement,
  type: 'barcode' | 'qrcode',
  isIOS: boolean
): string {
  const ctx = canvas.getContext('2d');
  if (!ctx) return '';
  const videoW = video.videoWidth;
  const videoH = video.videoHeight;
  const region = {
    x: videoW * 0.025,
    y: videoH * 0.4,
    width: videoW * 0.95,
    height: videoH * 0.20 
  };
  if (type === 'qrcode') {
    region.x = videoW * 0.1;
    region.y = videoH * 0.1;
    region.width = videoW * 0.8;
    region.height = videoH * 0.8;
  }
  canvas.width = region.width;
  canvas.height = region.height;

  if (isIOS) {
    ctx.drawImage(
      video,
      region.x, region.y, region.width, region.height,
      0, 0, canvas.width, canvas.height               
    );
  } else {
    ctx.drawImage(
      video,
      region.x, region.y, region.width, region.height,
      0, 0, region.width, region.height               
    );
  }
  
  return canvas.toDataURL('image/png').split(',')[1];
}

export function drawBoundingBox(canvas: HTMLCanvasElement, box: { x: number; y: number; width: number; height: number }) {
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  ctx.save();
  ctx.strokeStyle = 'rgba(0,255,0,0.8)';
  ctx.lineWidth = 4;
  ctx.beginPath();
  ctx.rect(box.x, box.y, box.width, box.height);
  ctx.stroke();
  ctx.restore();
}
