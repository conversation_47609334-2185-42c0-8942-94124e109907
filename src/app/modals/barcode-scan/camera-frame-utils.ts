// Utility functions for camera frame capture and canvas drawing

// Function to detect Samsung devices
function isSamsungDevice(): boolean {
  const userAgent = navigator.userAgent.toLowerCase();
  return userAgent.includes('samsung') || userAgent.includes('sm-') || userAgent.includes('galaxy');
}

export async function getCameraStream(videoElement: HTMLVideoElement, enableTorch: boolean = false): Promise<MediaStream> {
  const isSamsung = isSamsungDevice();
  console.log('Device detection - Samsung:', isSamsung, 'User Agent:', navigator.userAgent);

  const constraints: MediaStreamConstraints = {
    video: {
      facingMode: 'environment',
      width: { ideal: 1280 },
      height: { ideal: 720 },
      // For Samsung devices, try different constraint approaches
      ...(enableTorch && isSamsung && {
        advanced: [{ torch: true } as any],
        torch: true
      }),
      // For non-Samsung devices, use standard approach
      ...(enableTorch && !isSamsung && { advanced: [{ torch: true } as any] })
    }
  };

  let stream: MediaStream;

  try {
    // First try with torch constraint if requested
    stream = await navigator.mediaDevices.getUserMedia(constraints);
  } catch (error) {
    console.warn('Failed to get camera stream with torch constraint, trying without:', error);
    // Fallback: get stream without torch constraint
    const fallbackConstraints: MediaStreamConstraints = {
      video: {
        facingMode: 'environment',
        width: { ideal: 1280 },
        height: { ideal: 720 }
      }
    };
    stream = await navigator.mediaDevices.getUserMedia(fallbackConstraints);
  }

  // Try to enable torch after getting the stream (for devices that don't support initial constraint)
  if (enableTorch) {
    try {
      const track = stream.getVideoTracks()[0];
      const capabilities = track.getCapabilities();
      const settings = track.getSettings();

      console.log('Camera capabilities:', capabilities);
      console.log('Camera settings:', settings);

      if ('torch' in capabilities) {
        // For Samsung devices, try multiple approaches
        try {
          // Method 1: Direct constraint application
          await track.applyConstraints({
            advanced: [{ torch: true } as any]
          });
          console.log('Torch enabled successfully via advanced constraints');
        } catch (advancedError) {
          console.warn('Advanced constraints failed, trying basic constraints:', advancedError);
          // Method 2: Basic constraint application
          try {
            await track.applyConstraints({
              torch: true
            } as any);
            console.log('Torch enabled successfully via basic constraints');
          } catch (basicError) {
            console.warn('Basic constraints also failed:', basicError);
          }
        }
      } else {
        console.log('Torch not supported on this device - capabilities:', Object.keys(capabilities));
      }
    } catch (error) {
      console.warn('Failed to enable torch:', error);
    }
  }

  return stream;
}

// Function to toggle torch on existing stream
export async function toggleTorch(stream: MediaStream, enable: boolean): Promise<boolean> {
  try {
    const track = stream.getVideoTracks()[0];
    const capabilities = track.getCapabilities();
    const settings = track.getSettings();
    const isSamsung = isSamsungDevice();

    console.log(`Attempting to ${enable ? 'enable' : 'disable'} torch on ${isSamsung ? 'Samsung' : 'non-Samsung'} device`);
    console.log('Current settings:', settings);
    console.log('Capabilities:', capabilities);

    if ('torch' in capabilities) {
      // Try multiple methods for device compatibility, with Samsung-specific approaches
      let success = false;

      if (isSamsung) {
        // Samsung-specific methods (try direct constraint first)
        try {
          await track.applyConstraints({
            torch: enable
          } as any);
          console.log(`Torch ${enable ? 'enabled' : 'disabled'} successfully via Samsung direct constraints`);
          success = true;
        } catch (samsungDirectError) {
          console.warn('Samsung direct constraints failed:', samsungDirectError);

          // Fallback to advanced constraints for Samsung
          try {
            await track.applyConstraints({
              advanced: [{ torch: enable } as any]
            });
            console.log(`Torch ${enable ? 'enabled' : 'disabled'} successfully via Samsung advanced constraints`);
            success = true;
          } catch (samsungAdvancedError) {
            console.warn('Samsung advanced constraints failed:', samsungAdvancedError);
          }
        }
      } else {
        // Standard method for non-Samsung devices
        try {
          await track.applyConstraints({
            advanced: [{ torch: enable } as any]
          });
          console.log(`Torch ${enable ? 'enabled' : 'disabled'} successfully via standard advanced constraints`);
          success = true;
        } catch (standardError) {
          console.warn('Standard advanced constraints failed:', standardError);

          // Fallback to direct constraint
          try {
            await track.applyConstraints({
              torch: enable
            } as any);
            console.log(`Torch ${enable ? 'enabled' : 'disabled'} successfully via fallback direct constraints`);
            success = true;
          } catch (fallbackError) {
            console.warn('Fallback direct constraints failed:', fallbackError);
          }
        }
      }

      // Final fallback method for both device types
      if (!success) {
        try {
          await track.applyConstraints({
            video: {
              torch: enable
            }
          } as any);
          console.log(`Torch ${enable ? 'enabled' : 'disabled'} successfully via final fallback video constraints`);
          success = true;
        } catch (finalError) {
          console.warn('Final fallback video constraints also failed:', finalError);
        }
      }

      // Verify the torch state after applying constraints
      if (success) {
        setTimeout(() => {
          const newSettings = track.getSettings();
          console.log('New settings after torch toggle:', newSettings);
          const actualTorchState = (newSettings as any).torch;
          if (actualTorchState !== enable) {
            console.warn(`Torch state mismatch: requested ${enable}, actual ${actualTorchState}`);
          }
        }, 100);
      }

      return success;
    } else {
      console.log('Torch not supported on this device - available capabilities:', Object.keys(capabilities));
      return false;
    }
  } catch (error) {
    console.warn(`Failed to ${enable ? 'enable' : 'disable'} torch:`, error);
    return false;
  }
}

// Function to check torch support and provide detailed info
export async function checkTorchSupport(): Promise<{supported: boolean, details: string}> {
  try {
    // Get a temporary stream to check capabilities
    const tempStream = await navigator.mediaDevices.getUserMedia({
      video: { facingMode: 'environment' }
    });

    const track = tempStream.getVideoTracks()[0];
    const capabilities = track.getCapabilities();
    const settings = track.getSettings();

    // Clean up the temporary stream
    track.stop();

    const supported = 'torch' in capabilities;
    const details = `
      Device: ${navigator.userAgent}
      Torch supported: ${supported}
      Capabilities: ${JSON.stringify(capabilities, null, 2)}
      Settings: ${JSON.stringify(settings, null, 2)}
    `;

    console.log('Torch support check:', details);
    return { supported, details };
  } catch (error) {
    const details = `Failed to check torch support: ${error}`;
    console.warn(details);
    return { supported: false, details };
  }
}

export function captureFrame(
  video: HTMLVideoElement,
  canvas: HTMLCanvasElement,
  type: 'barcode' | 'qrcode',
  isIOS: boolean
): string {
  const ctx = canvas.getContext('2d');
  if (!ctx) return '';
  const videoW = video.videoWidth;
  const videoH = video.videoHeight;
  const region = {
    x: videoW * 0.025,
    y: videoH * 0.4,
    width: videoW * 0.95,
    height: videoH * 0.20 
  };
  if (type === 'qrcode') {
    region.x = videoW * 0.1;
    region.y = videoH * 0.1;
    region.width = videoW * 0.8;
    region.height = videoH * 0.8;
  }
  canvas.width = region.width;
  canvas.height = region.height;

  if (isIOS) {
    ctx.drawImage(
      video,
      region.x, region.y, region.width, region.height,
      0, 0, canvas.width, canvas.height               
    );
  } else {
    ctx.drawImage(
      video,
      region.x, region.y, region.width, region.height,
      0, 0, region.width, region.height               
    );
  }
  
  return canvas.toDataURL('image/png').split(',')[1];
}

export function drawBoundingBox(canvas: HTMLCanvasElement, box: { x: number; y: number; width: number; height: number }) {
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  ctx.save();
  ctx.strokeStyle = 'rgba(0,255,0,0.8)';
  ctx.lineWidth = 4;
  ctx.beginPath();
  ctx.rect(box.x, box.y, box.width, box.height);
  ctx.stroke();
  ctx.restore();
}
