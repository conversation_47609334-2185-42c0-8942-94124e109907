<ion-header *ngIf="selectedTemplate != 'olivery_pro_max_dashboard'">
  <ion-toolbar>
    <ion-row style="margin-bottom: 1vh;padding: 5px;">
      <ion-col size="6" class="center-header">
        <ion-chip (click)="openUserPage()" color="dark" style="padding-block: 0; margin:0; zoom:1.3">
          <ion-avatar style="border: 2px solid var(--ion-color-dark);">
            <img [src]="userImageUrl" alt="../../../../assets/icon/user-dash.svg"/>
          </ion-avatar>
          <ion-label style="font-weight: bolder; font-size: .8rem;margin: 5px;">
            {{ 'WELCOME' | translate }}
          </ion-label>
          <ion-label style="font-weight: bolder; font-size: .8rem;">
            {{ userName }}
          </ion-label>
        </ion-chip>
      </ion-col>
      <ion-col size="6" class="center-header ion-no-padding" style="justify-content: end;">
        <div *ngIf="(userRole === 'rb_delivery.role_driver' || ( userRole === 'rb_delivery.role_business' && clientConfigurations && clientConfigurations.show_online_in_dashboard_visibility)) && onDmandService.onDemandServer && !loadingDriverState" [ngClass]="userIsOnline?'custom-toggle':'custom-toggle-no'" [class.toggled]="userIsOnline" (click)="userOnlineToggle()">
          <span [ngClass]="useltrClass?'toggle-option yes-option':'toggle-option yes-option-rtl'" *ngIf="userIsOnline">{{'ONLINE'|translate}}</span>
          <span [ngClass]="useltrClass?'toggle-option no-option':'toggle-option no-option-rtl'"  *ngIf="!userIsOnline">{{'OFFLINE'|translate}}</span>
          <div class="toggle-handle"></div>
          
        </div>
        <ion-spinner *ngIf="loadingDriverState" name="dots"></ion-spinner>
        <ion-button *ngIf="managerPhones.length>0" fill="clear" size="small" color="primary" (click)="contactManager()">
          <ion-icon src="/assets/icon/support.svg" size="small" style="font-size: 2rem;" ></ion-icon>
        </ion-button>
        
        <ion-nav-link *ngIf="userRole === 'rb_delivery.role_business' && showAnnouncement" router-direction="forward" [component]="announcementsComponent">
          <ion-button fill="clear" [color]="unseenAnnouncementCount>0?'primary':'dark'">
            <ion-badge size="small" class="notification-badge" *ngIf="unseenAnnouncementCount>0" slot="start">{{unseenAnnouncementCount>99?'99+':unseenAnnouncementCount}}</ion-badge>
            <ion-icon src="/assets/icon/announcement.svg" style="font-size: 2rem;"></ion-icon>
          </ion-button>
        </ion-nav-link>

        <ion-nav-link router-direction="forward" [component]="notificationCenterComponent">
          <ion-button fill="clear" style="width: 45px;--padding-start: 0; --padding-end: 0;" [color]="notificationUnSeenCount>0?'primary':'dark'">
            <ion-badge size="small" class="notification-badge" *ngIf="notificationUnSeenCount>0" slot="start">{{notificationUnSeenCount>99?'99+':notificationUnSeenCount}}</ion-badge>
            <ion-icon name="notifications-outline" style="font-size: 2rem;"></ion-icon>
          </ion-button>
        </ion-nav-link>
        
      </ion-col>
    </ion-row>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true">
  
  <ion-refresher slot="fixed" (ionRefresh)="handleRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <!-- <ion-popover class="popup arrow-top" [isOpen]="isPopoverOpen" [event]="popoverEvent">
    <ng-template>
      <div class="announcement-container">
        <div class="announcement-card">
          <div class="announcement-image">
            <a [href]="announcements[0].announcement_link">
              <img [src]="env.url + '/web/image?model=rb_delivery.announcement&id='+announcements[0].id+'&field=images'" alt="Announcement Image" />
            </a>
          </div>
          <div class="announcement-content">
            <h2>'Announcement Title Ris;lkdf;lksdkf;ks;ldkafksa;lkdf;lksad;lkf;lkgfojokaslght Here...'</h2>
            <div class="separator"></div>
            <p>'Announcement descriptiodsjsjf;jsjflkjaskjff;ljsdjflkjlksjfljas;ljd;ajfjja;ldsjfjasdf;asj;lfjjsdfj;lasjfn...'</p>
          </div>
        </div>
      </div>
    </ng-template>
  </ion-popover> -->
  
  <ng-container *ngIf="!pageLoading">
    <app-dynamic-dashboard *ngIf="selectedTemplate === 'dynamic_dashboard'"></app-dynamic-dashboard>
    <app-template-one-dashboard *ngIf="selectedTemplate === 'redline_dashboard'"></app-template-one-dashboard>
    <app-template-two-dashboard *ngIf="selectedTemplate === 'bosta_dashboard'"></app-template-two-dashboard>
    <app-template-four-dashboard *ngIf="selectedTemplate === 'united_dashboard'"></app-template-four-dashboard>
    <app-template-five-dashboard *ngIf="selectedTemplate === 'tornado_dashboard'"></app-template-five-dashboard>
    <app-template-six-dashboard *ngIf="selectedTemplate === 'olivery_pro_max_dashboard'"></app-template-six-dashboard>
  </ng-container>

  <!-- These lines are added to test onesignal.. uncomment these line to activate test -->
  <!-- <ion-list >
    <ion-item>
      <ion-button (click)="initOneSignal()">init</ion-button>
    </ion-item>
    <ion-item>
      <ion-button (click)="onesignalOptin()">optin</ion-button>
    </ion-item>
    <ion-item>
      <ion-button (click)="onesignalOptout()">optout</ion-button>
    </ion-item>
    <ion-item>
      <ion-button (click)="addListeners()">addListeners</ion-button>
    </ion-item>
    <ion-item>
      <ion-button (click)="removeListeners()">removeListeners</ion-button>
    </ion-item>
  </ion-list> -->
</ion-content>


