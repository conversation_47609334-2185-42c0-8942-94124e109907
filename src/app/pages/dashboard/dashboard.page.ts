import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject, combineLatest, filter, take, takeUntil } from 'rxjs';
import { UserState } from 'src/app/ngrx-store/user/store';
import { OdooJsonRPC } from 'src/app/services/odooJsonRPC';
import * as userSelectors from '../../ngrx-store/user/store/selectors'
import { ModelsFilterState } from 'src/app/ngrx-store/models-filter/store';
import * as modelsFilterActions from '../../ngrx-store/models-filter/store/actions'
import * as userActions from 'src/app/ngrx-store/user/store/actions';
import { NotificationCenterState } from 'src/app/ngrx-store/notification-center/store';
import * as notificationSelectors from '../../ngrx-store/notification-center/store/selectors'
import * as notificationActions from '../../ngrx-store/notification-center/store/actions'
import { environment } from 'src/environments/environment';
import { Router } from '@angular/router';
import { ModalController, ToastController } from '@ionic/angular';
import { NotificationCenterPage } from '../notification-center/notification-center.page';
import { OnesignalService } from 'src/app/services/onesignal-service';
import { TranslateService } from '@ngx-translate/core';
import { StatusState } from 'src/app/ngrx-store/status/store';
import { Storage } from '@ionic/storage-angular';
import { Ondemandservice } from 'src/app/services/ondemand-service';
import { RecordCreatorPage } from 'src/app/modals/record-creator/record-creator.page';
import { ContactUsComponent } from 'src/app/components/contact-us/contact-us.component';
import { GeneralConfigurationState } from 'src/app/ngrx-store/general-configuration/store';
import * as generalConfigurationsSelector from '../../ngrx-store/general-configuration/store/selectors';
import { IGeneralConfiguration } from 'src/app/ngrx-store/general-configuration/model';

import { ClientConfigurationState } from '../../ngrx-store/client-configuration/store/state';
import * as clientConfigurationsActions from '../..//ngrx-store/client-configuration/store/actions';
import * as clientConfigurationsSelector from '../..//ngrx-store/client-configuration/store/selectors';


import { AnnouncementsComponent } from '../announcements/announcements.component';

@Component({
  selector: 'app-dashboard',
  templateUrl: 'dashboard.page.html',
  styleUrls: ['dashboard.page.scss']
})
export class DashboardPage implements OnInit{
  // TODO get value from web when implement Odoo api
  selectedTemplate! : string 
  pageLoading : boolean = true
  notificationUnSeenCount = 0
  userName :string = ''
  userImageUrl = ''
  destroyed$ = new Subject<any>();
  userId: any;
  userIsOnline= false
  notificationCenterComponent=NotificationCenterPage
  userRole: any;
  isToggled =false;
  useltrClass: boolean = true;
  loadingDriverState : boolean = false
  managerPhones: any[] = [];
  unseenAnnouncementCount = 0
  announcements:any[] = []
  isPopoverOpen = true;
  popoverEvent: any;
  showAnnouncement:boolean=false
  announcementsComponent=AnnouncementsComponent
  env = environment;
  clientConfigurations: any = {}
  
  constructor(
    private odooRPC: OdooJsonRPC,
    private userStore :Store<UserState>,
    private modelsFilter :Store<ModelsFilterState>,
    private notificationCenterStore :Store<NotificationCenterState>,
    private onesignal:OnesignalService,
    private userState: Store<UserState>,
    private translate : TranslateService,
    private toastCtrl : ToastController,
    private storage : Storage,
    public onDmandService:Ondemandservice,
    private modalCtrl: ModalController,
    private generalConfigurationStore: Store<GeneralConfigurationState>,
    private clientConfigurationStore: Store<ClientConfigurationState>,
  ) {}

  ngOnInit(): void {
    
    if (document.dir === 'ltr') {
      this.useltrClass = true
    }
    else{
      this.useltrClass = false
    }
    this.getSelectedTemplate()
    this.getUnseenNotificationCount()
    this.getUserModelsFilter()
    this.getUnseenAnnouncementCount()
    this.checkAnnouncement()
    this.getClientConfiguration()
  }

  ionViewDidEnter(){
    this.loadUnseenNotificationsCount()
  }

  ngAfterContentInit() {
    this.getUserInfo()
  }

  getUnseenNotificationCount() {
    this.notificationCenterStore.select(notificationSelectors.selectUnseenCount).pipe(takeUntil(this.destroyed$)).subscribe( unseenCount =>{
      this.notificationUnSeenCount = unseenCount
    })
  }
  
  getUnseenAnnouncementCount() {
    this.notificationCenterStore
      .select(notificationSelectors.selectUnseenAnnouncementCountCount)
      .pipe(takeUntil(this.destroyed$))
      .subscribe(unseenAnnouncementCount => {
        this.unseenAnnouncementCount = unseenAnnouncementCount;
      });
  }
  
  async handleRefresh(event:any){
    this.pageLoading = true
    this.getSelectedTemplate()
    this.loadUnseenNotificationsCount()
    event.target.complete();
  }

  loadUnseenNotificationsCount() {
    this.notificationCenterStore.dispatch(new notificationActions.LoadUnseenCountHttp({userId:this.userId}))
    this.notificationCenterStore.dispatch(new notificationActions.LoadUnseenAnnouncementCountHttp({userId:this.userId}))
  }

  getUserInfo(){
    this.storage.get('user_info').then(async userInfo=>{
      if(userInfo && Object.keys(userInfo).length > 0 && userInfo[0] ){
        this.userRole = userInfo[0].role_code
        this.userName = userInfo[0].username
        this.userId = userInfo[0].id
        this.userImageUrl = environment.url+'/api/get_image?model=rb_delivery.user&id='+userInfo[0].id+'&field=user_image'
        this.loadUnseenNotificationsCount()
        this.userStore.select(userSelectors.selectUserById(this.userId)).pipe(filter(data => data && Object.keys(data).length > 0),take(1)).subscribe(userData=>{
          this.userIsOnline = userData.online
        })
        if(userInfo[0].account_manager_mobile){
          this.managerPhones.push({name:'ACCOUNT_MANAGER_PHONE',phone:userInfo[0].account_manager_mobile})
        }
        let generalConfig = await this.generalConfigurationStore.select(generalConfigurationsSelector.selectAllData).pipe(filter((keys) => keys && keys.length > 0), take(1)).toPromise() as IGeneralConfiguration[]
        if(generalConfig[0]['support_phone']){
          this.managerPhones.push({name:'SUPPORT_PHONE',phone:generalConfig[0]['support_phone']})
        }
      }
    })
  }


  openUserPage() {
    
    combineLatest([
      this.userStore.select(userSelectors.selectFields),
    ])
    .pipe(take(1)).subscribe(([fields]) => { 
      this.odooRPC.searchRead('rb_delivery.user', [['id', '=', this.userId]], fields, 1,0,'create_date desc').then(data=>{
        if (data && data.body && data.body.result && data.body.result.result && data.body.result.result[0]) {
          this.modalCtrl.create({
            component: RecordCreatorPage,
            componentProps: {
              formName: 'user_form',
              field: {
                'name': this.translate.instant('USER')
              },
              disableEdit:false,
              record:data.body.result.result[0]
            }
          }).then(modal => {
            modal.present()
          })
        }
      })
    })
  }

  getSelectedTemplate(){
    this.odooRPC.searchRead('rb_delivery.dashboard_templates',[['template_selected','=',true]],['name','title'],0,0,'id DESC').then(dashboardSelected =>{
      if( dashboardSelected.body.result && dashboardSelected.body.result.success){
        this.selectedTemplate = dashboardSelected.body.result.result[0].name
        this.pageLoading = false
      }
      else{
        const toast = this.toastCtrl.create({
          header: this.translate.instant('Template Not Selected'),
          message: this.translate.instant('Please select a template'),
          duration: 6000,
          position: 'top',
          mode: 'ios',
          buttons: [
            {
              text: 'close',
              role: 'cancel',
              handler: () => {
                console.log('Close clicked');
              }
            }
          ],
        });
        toast.then(tst => tst.present());
      }
    })
  }

   async checkAnnouncement() {
      let announcements = await this.odooRPC.searchRead('rb_delivery.announcement', [], [], 1, 0, 'create_date desc')
      if (announcements.body && announcements.body.result && announcements.body.result.result && announcements.body.result.result.length > 0) {
        this.showAnnouncement = true
      }
    }

  async contactManager(){
      this.modalCtrl.create({
        component: ContactUsComponent,
        componentProps: { 
          title:'CONTACT_MANAGER',
          phoneNumbers: this.managerPhones
        },
        cssClass: 'windowed-medium-modal',
        mode: 'ios',
      }).then(modal => modal.present());
    }


  getUserModelsFilter(){
    this.modelsFilter.dispatch(new modelsFilterActions.LoadHttp({}))
  }


  initOneSignal(){
    this.onesignal.oneSignalInit()
  }


  onesignalOptin(){
    this.onesignal.oneSignalOptIn()
  }

  onesignalOptout(){
    this.onesignal.oneSignalOptOut()
  }

  addListeners(){
    this.onesignal.addAllListeners()
  }
  
  removeListeners(){
    this.onesignal.removeListeners()
  }

  checkOnline(event:any){
    if(event && event.detail){
      this.userState.dispatch(new userActions.UpdateHttp({id:this.userId, values:{'online':event.detail.checked} }))
    }
  }

  userOnlineToggle(){
    this.userIsOnline = !this.userIsOnline;
    this.loadingDriverState = true
    this.userState.dispatch(new userActions.UpdateHttp({id:this.userId, values:{'online':this.userIsOnline} }))
    combineLatest([
      this.userStore.select(userSelectors.selectUserById(this.userId)),
      this.userStore.select(userSelectors.selectUploading),
    ])
    .pipe(filter(([userData,uploading])=>!uploading),take(1)).subscribe(([userData,uploading]) => { 
      this.userIsOnline = userData.online
      this.loadingDriverState = false
    })
    
  }
  getClientConfiguration() {
    this.clientConfigurationStore.select(clientConfigurationsSelector.selectClientConfigurationByKeys(['show_online_in_dashboard_visibility'])).pipe(filter((keys) => keys && keys.length > 0), take(1)).subscribe(configurationKeys => {
      if (configurationKeys && configurationKeys.length > 0) {
        for (let configurationKey of configurationKeys) {
          this.clientConfigurations[configurationKey.key] = configurationKey.value
        }
      }
    })
  }
}