import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AlertController, AlertInput, ItemReorderCustomEvent, InfiniteScrollCustomEvent, ModalController, ToastController, ActionSheetController, ActionSheetButton, SelectCustomEvent, RefresherCustomEvent, AnimationController, IonSelect, LoadingController } from '@ionic/angular';
import { Store } from '@ngrx/store';
import { Observable, Subject, combineLatest, filter, map, of, skip, take, takeUntil } from 'rxjs';
import { RecordCreatorPage } from 'src/app/modals/record-creator/record-creator.page';
import { IOrder, orderFieldsForRouteOptimization } from 'src/app/ngrx-store/order/model';
import { IGroup, RecordCardStructure, RecordStructureService } from 'src/app/services/record-structure-service';
import { OrderState, selectGroupedOrders } from 'src/app/ngrx-store/order/store';
import * as orderActions from 'src/app/ngrx-store/order/store/actions';
import * as orderSelectors from 'src/app/ngrx-store/order/store/selectors';
import { ActionService } from 'src/app/services/actions-service';
import { StatusState } from 'src/app/ngrx-store/status/store';
import * as statusActions from 'src/app/ngrx-store/status/store/actions';
import * as statusSelector from 'src/app/ngrx-store/status/store/selectors';
import { Storage } from '@ionic/storage-angular';
import { IUser } from 'src/app/ngrx-store/user/model';
import { Subscription } from 'rxjs';
import { FormControl } from '@angular/forms';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';
import { PrintReportService } from 'src/app/services/print-reports-service';
import { OdooJsonRPC } from 'src/app/services/odooJsonRPC';
import { StatusActionService } from 'src/app/services/statusAction';
import { TimeService } from 'src/app/services/time-services';
import { DateSelectorComponent } from 'src/app/modals/date-selector/date-selector.component';
import { UpdateMultiOrdersComponent } from 'src/app/components/update-multi-orders/update-multi-orders.component';
import { BarcodeScanComponent } from 'src/app/modals/barcode-scan/barcode-scan.component';
import { FollowerOrderScannerComponent } from 'src/app/components/follower-order-scan/follower-order-scanner.component';
import { RecordItemComponent } from 'src/app/components/record-item/record-item.component';
import { MessageState } from 'src/app/ngrx-store/message/store';
import * as messageActions from 'src/app/ngrx-store/message/store/actions';
import * as messageSelectors from 'src/app/ngrx-store/message/store/selectors';
import { ModelsFilterState } from 'src/app/ngrx-store/models-filter/store';
import * as modelsFilterSelectors from 'src/app/ngrx-store/models-filter/store/selectors';
import { OrderChatComponent } from 'src/app/components/order-chat/order-chat.component';
import { DynamicSelectionComponent } from 'src/app/components/dynamic-selection/dynamic-selection.component';
import { OrderHistoryComponent } from 'src/app/modals/order-history/order-history.component';
import { dialogService } from 'src/app/services/error-handlar-service';
import { InAppBrowser, InAppBrowserObject } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { IconsMap } from 'src/app/assets-maps/icons-map';
import { Browser } from '@capacitor/browser';
import { FollowOrderService } from 'src/app/services/follow-orders-service';
import { ClientConfigurationState } from 'src/app/ngrx-store/client-configuration/store/state';
import * as clientConfigurationsSelector from 'src/app/ngrx-store/client-configuration/store/selectors';
import { CreateCollectionConfirmationComponent } from 'src/app/components/create-collection-confirmation/create-collection-confirmation.component';
import { SuccessCreateCollectionComponent } from 'src/app/components/success-create-collection/success-create-collection.component';
import { LocationSelectorComponent } from 'src/app/modals/location-selector/location-selector.component';
import { ObjectService } from 'src/app/services/object-service';
import { IStatus } from 'src/app/ngrx-store/status/model';
import { AttachmentModal } from 'src/app/modals/attachments-modal/attachment-modal';
import { RouteCollectionComponent } from 'src/app/components/route-collection/route-collection.component';
import { CreateRouteCollectionComponent } from 'src/app/components/create-route-collection/create-route-collection.component';
import { NativeSettings, AndroidSettings, IOSSettings } from 'capacitor-native-settings';
import { Geolocation } from '@capacitor/geolocation'
import { LocationService } from 'src/app/services/location-service';
import { OrderDetailComponent } from 'src/app/components/order-detail/order-detail.component';
import { FollowOrderCreatorComponent } from 'src/app/components/follow-order-creator/follow-order-creator.component';
import { OtpVerificationComponent } from 'src/app/modals/otp-verification/otp-verification.component';

@Component({
  selector: 'app-orders',
  templateUrl: 'orders.page.html',
  styleUrls: ['orders.page.scss']
})
export class OrdersPage implements OnInit {
  @ViewChild('stateSelector') stateSelector : IonSelect | undefined;
  destroyed$ = new Subject<any>();
  orderCardsStructures$!:Observable<RecordCardStructure[]>
  searchControl: FormControl = new FormControl()
  private searchSubscription?: Subscription;
  orders$!:Observable<any[]>
  domain: any[]=[]
  loading!: Observable<boolean>;
  isScrolling : boolean = false
  orders!: IOrder[];
  selectedOption!: {name:string,discription:string};
  groupingLevels: string[] = [];
  groupOptionLabels:string []=[]
  selectedGroups:IGroup[]=[]
  groupedOrders: IGroup[] = [];
  isGroupByEnabled: boolean = false;
  isGroupOpened: boolean = false;
  groupByDomain: any[]=[]
  initeScrollCustomEvent!: InfiniteScrollCustomEvent;
  isFiltered!: boolean;
  isSelectEnabled = false
  selectedOrderIds :any[]= []
  loadedOrdersCount = 0
  scrollLimit:number=50
  orderActions: any[]=[];
  userInfo : any
  isRouteReorderEnabled!:boolean
  statusesHavingAccess:any[]=[]
  defaultSearchFields: any[]=[];
  selectedFilterField: any;
  filterFields: any;
  groupByFields: any;
  groupBySelectedField: any[]=[];
  neededFields: any;
  nextStatuses: any;
  orderReports !: any;
  orderIds!: number[];
  firstSelectedOrderOffset!: number | undefined;
  hideSelection: boolean = true;
  dateDomain: any=[]
  selectedDateFilter: any;
  shownOnSegmentStatuses:any[]=[]
  statusChanged: number[] = []
  currentStatusFilter:any=false;
  routeCollectionPage = false;
  statusFilterPlaceHolder:string = ''
  triggerParentFunction!: Function;
  digits: string="1.2-2"
  dateFilter=[{
    name:"TODAY"
  },
  {
    name:"YESTERDAY"
  },
  {
    name:"THIS_WEEK"
  },
  {
    name:"THIS_MONTH"
  },
  {
    name:"THIS_YEAR"
  },
  {
    name:"SELECTED_DATE"
  }
]
  searchDomain: any[]=[];
  ordersCount!: number;
  statusDomain: any[]=[]
  isChangingStatusFilter: boolean = true
  reordering: boolean =false
  showCollapse: boolean = false
  isAllCollapsed: boolean = false;
  loadingNextStatuses: boolean = false;
  isActionSheetOpen: any = false;
  infiniteScrollDisabled!: boolean;
  scanActivate = false
  searchFilterPlaceholder:string = 'ALL'
  searchValue: string ='';
  showReordering: boolean = false;
  selectedGroupOption!: string;
  paymentTypes: any;
  infiniteScroll=new Subject<any>()
  action!: string;
  actionAfterLoad: string | undefined;
  buttonsToHide: string[] = []
  loadIntoTempOrders: boolean = false;
  tempDomain: string[] = []
  showRouteOptimization: any;
  routeCollectionId:any
  routeId: any
  createCommunicationLogMessage:any
  groupByCardFields:any
  orderSlots: any;
  useSlotDateFilter:boolean = false;
  constructor(
    private orderStore: Store<OrderState>,
    private modalCtrl: ModalController,
    private route: ActivatedRoute,
    private router: Router,
    private alertController: AlertController,
    private recordStructreService:RecordStructureService,
    private translate : TranslateService,
    private actionService : ActionService,
    private toastCtrl : ToastController,
    private statusStore : Store<StatusState>,
    private storage : Storage,
    private alertCtrl: AlertController,
    private printService : PrintReportService,
    private odooRPC: OdooJsonRPC,
    private actionSheetCtrl:ActionSheetController,
    private statusActionService:StatusActionService,
    private timeService: TimeService,
    private animationCtrl:AnimationController,
    private messageStore : Store<MessageState>,
    private modelsFilterStore :Store<ModelsFilterState>,
    private dialogService: dialogService,
    private iab:InAppBrowser,
    private followOrdersService: FollowOrderService,
    private loadingCtrl:LoadingController,
    private clientConfigurationStore: Store<ClientConfigurationState>,
    private objService: ObjectService,
    private locationService: LocationService
    ) { }
  ngOnInit() {
    this.defineOrderStructures()
    this.listenToInfiniteScroll()
    this.loadPaymentTypes()
    this.loadingListener()
    this.getDefaultSearchFields()
    this.getFilterFields()
    this.getGroupByFields()
    this.getOrderSlots()
    this.orderStore.dispatch(new orderActions.LoadDefaultSearchFilter({}));
    this.orderStore.dispatch(new orderActions.LoadFilterFields({}));
    this.initSearch();
    this.listenToParams()
    this.getUserInfo()
    this.fetchActions()
    this.loadMessages()
    this.getStatusFilterName()
  }
  defineOrderStructures() {
    this.orderCardsStructures$ = this.orderStore.select(orderSelectors.selectOrdersSortedById())
    .pipe(map(structuredOrders=>{
      
      this.loadedOrdersCount = structuredOrders.length
      if(this.initeScrollCustomEvent){
        this.initeScrollCustomEvent.target.complete()
      }
      this.orderIds = structuredOrders.map(order=>order.id)
      if(structuredOrders.length>0 && structuredOrders[0].header.show_collapse){
        this.showCollapse=true
        this.isAllCollapsed=structuredOrders[0].header.is_collapsed
      }else{
        this.showCollapse=false
      }

      if(this.actionAfterLoad && structuredOrders.length>0){
        this.doFunction(this.actionAfterLoad as OperationTypes,structuredOrders[0].id)
      }
      return structuredOrders
    }))
  }
  loadStatuses(){
    this.statusStore.dispatch(new statusActions.LoadHttp({search:[['status_type','=','olivery_order']]}))
    this.statusStore.select(statusSelector.selectOrderShownOnSegmentStatus(this.userInfo[0].group_id[0])).pipe(filter(statuses=>statuses.length>0),take(1)).subscribe(statuses=>{
      this.shownOnSegmentStatuses=statuses
    })
    this.statusStore.select(statusSelector.selectHavingAccessStatus(this.userInfo[0].group_id[0])).pipe(filter(statuses=>statuses.length>0),take(1)).subscribe(statuses=>{
      this.statusesHavingAccess=statuses
    })
  }
  getUserInfo(){
    this.storage.get('user_info').then(userInfo=>{
      if(userInfo){
        this.userInfo = userInfo
        this.loadStatuses()
        if(userInfo[0]){
          this.clientConfigurationStore.select(clientConfigurationsSelector.selectClientConfigurationByKeys(['show_route_optimization_for_driver','global_dicimal_value','create_log_message_for_appx_communication','filter_orders_by_slot_dates'])).pipe(take(1)).subscribe(configurationKeys=>{
            for (let configuration of configurationKeys) {
              if (configuration && configuration.key == 'show_route_optimization_for_driver' && userInfo[0].role_code == 'rb_delivery.role_driver') {
                this.showRouteOptimization = configuration.value
              }
              else if (configuration && configuration.key == 'global_dicimal_value') {
                this.digits = `1.${configuration.text}-${configuration.text}`
              }
              else if (configuration && configuration.key == 'create_log_message_for_appx_communication') {
                this.createCommunicationLogMessage = configuration.value
              }
              else if(configuration && configuration.key == 'filter_orders_by_slot_dates'){
                this.useSlotDateFilter = configuration.value
                

              }
            }
          })
        }
      }
    })
  }
  loadingListener() {
    this.loading = this.orderStore.select(orderSelectors.selectFirstLoading).pipe(map(loading => {
      console.log(loading)
      return loading
    }))
  }

  loadMessages(){
    this.messageStore.dispatch(new messageActions.LoadHttp({}))
  }

  loadOrders(actionAfterLoad?:string) {
    if (!this.loadIntoTempOrders) {
      this.actionAfterLoad = actionAfterLoad
      this.recordStructreService.fetchStructure('order_card').then(structueResponse=>{
        if(structueResponse && structueResponse.success){
          if (this.routeCollectionPage) {
            this.updateOrdersSearch(structueResponse.neededFields, 'route_sequence ASC')
            this.neededFields=structueResponse.neededFields
          } else {
            this.updateOrdersSearch(structueResponse.neededFields)
            this.neededFields=structueResponse.neededFields
          }

        }
      })
    } else {
      if (this.tempDomain.length < 1) {
        this.tempDomain = this.objService.deepClone(this.domain)
      }
      this.infiniteScrollDisabled = true;
      this.orderStore.dispatch(new orderActions.LoadHttp({search:this.domain,offset:0,addToTempOrders:true,limit:0}))
      combineLatest([
        this.orderStore.select(orderSelectors.selectAllTempDataStructures),
        this.orderStore.select(orderSelectors.selectLoading)
      ])
      .pipe(filter(([orders,loading])=>!loading && orders.length>0),take(1)).subscribe(([orderStructures,])=>{
        this.orderCardsStructures$ = of(orderStructures)
        this.getCountOfOrders()
        
      })
    }
  }
  clearFilter(){
    if (this.stateSelector) {
      this.stateSelector.value = false;
    }
    this.isFiltered=false
    this.domain = []
    this.searchDomain = []
    this.searchValue = ''
    this.statusDomain = []
    this.currentStatusFilter= false
    this.getStatusFilterName()
    this.router.navigate(['/tabs/orders'], {
      queryParams: {
        'domain': undefined,
        'sequence': undefined,
        'id': undefined,
        'action': undefined
      },
      queryParamsHandling: 'merge'
    })
  }
  listenToParams() {
    this.route.queryParams
      .pipe(takeUntil(this.destroyed$))
      .subscribe(params => {
        if (params && Object.keys(params).length > 0) {
          if('domain' in params){
            this.domain = JSON.parse(params['domain'])
            this.statusDomain = []
            this.currentStatusFilter= false
            this.getStatusFilterName()
          }
          if('sequence' in params){
            let sequence = JSON.parse(params['sequence'])
            if(sequence){
              const fieldsToAdd :any[] = ['sequence', 'reference_id'];
              for(let field of fieldsToAdd){
                if(!this.defaultSearchFields.includes(field)){
                  this.defaultSearchFields = [...this.defaultSearchFields, field];
                }
              }
              this.searchControl.setValue(sequence)
            }
          }
          if ('id' in params) {
            if (params['id'] == 'today_orders') {
              let startDate;
              let endDate;
              const handleDateRange = (startFunc:any, endFunc:any, range:string|null) => {
                startDate = this.timeService.dateTimeToOdooFormat(
                  this.timeService.getUTCFormat(startFunc(range))
                );
                endDate = this.timeService.dateTimeToOdooFormat(
                  this.timeService.getUTCFormat(endFunc(range))
                );
              };

              handleDateRange(this.timeService.getStartDate, this.timeService.getEndDate, null);

              this.filterByDate(startDate, endDate);
            }
          }
          this.isFiltered=true
        }
        else {
          if(this.domain.length == 0){
            this.domain = []
          }
          this.isFiltered=false
          this.searchControl.setValue(null)
        }
        if (params && Object.keys(params).length > 0 && 'action' in params) {
          this.loadOrders(params['action'])
        }
        else if(this.action){
          this.loadOrders(this.action)
        }
        else{
          this.loadOrders()
        }
      })
  }
  async getCountOfOrders(){
    let domain = await this.getDomain()
    if (domain.length == 0) {
      domain = this.objService.deepClone(this.tempDomain)

    }
    this.orderStore.dispatch(new orderActions.CountNumberOfOrdersHttp( domain ))
    combineLatest([
      this.orderStore.select(orderSelectors.selectAllOrdersCount),
      this.orderStore.select(orderSelectors.selectLoadingCount)
    ]).pipe(filter(([, loadingCount]) => !loadingCount), take(1)).subscribe(async ([ordersCount]) => {
        this.ordersCount = ordersCount
        this.isChangingStatusFilter = false
    })
  }
  ionViewDidLeave() {
    this.isScrolling = false
  }
  listenToInfiniteScroll(){
    this.infiniteScroll.pipe(debounceTime(1000),takeUntil(this.destroyed$)).subscribe(event=>{
      this.isScrolling = true
      this.orderStore.dispatch(new orderActions.LoadHttp({}))
      this.initeScrollCustomEvent = event as InfiniteScrollCustomEvent
    })
  }
  loadMoreOrders(event: Event) {
    this.infiniteScroll.next(event)
  }
  doFunction = (functionName: OperationTypes, orderId: number) => {
    functionName = functionName as OperationTypes 
    if (functionName in this)
      this[functionName](orderId)
    this.actionAfterLoad=undefined
  }

  callCustomer(orderId: number){
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      let buttons:ActionSheetButton[]=[]
      if(order.customer_mobile || order.second_mobile_number){
        buttons.push(
          {
            text:this.translate.instant('CALL_VIA_CELL'),
            handler:(()=>{
              this.callCustomerViaCell(orderId)
            }),
            icon:'call-outline'
          }
        )
      }
      if(order.cus_whatsapp_mobile || order.cus_second_whatsapp_mobile){
        buttons.push(
          {
            text:this.translate.instant('CALL_VIA_WHATSAPP'),
            handler:(()=>{
              this.callCustomerViaWhatsapp(orderId)
            }),
            icon:'logo-whatsapp'
          }
        )
      }
      let header = this.translate.instant('CALL')
      let errorMessage = this.translate.instant('NO_MOBILE_NUMBER_FOUND')
      this.showActionSheet(header,buttons,errorMessage)
    })
  }
  
  async solveStuck(orderId: number) {
    const alert = await this.alertController.create({
        mode: 'ios',
        header: this.translate.instant('SOLVE_STUCK'),
        inputs: [
            {
                name: 'solveStuck',
                type: 'text',
                placeholder: this.translate.instant('SOLVE_STUCK')
            }
        ],
        buttons: [
            {
                text: this.translate.instant('CANCEL'),
                role: 'cancel',
                cssClass: 'secondary'
            },
            {
                text: this.translate.instant('OK'),
                handler: async (data) => {
                    const response = await this.odooRPC.call('rb_delivery.order', 'solve_stuck', [
                        orderId, {
                            'solve_stuck_comment': data.solveStuck,
                        }
                    ]);
                    
                    if (response && response.body && response.body.result && response.body.result.success) {
                        this.loadOrders();
                    }
                }
            }
        ]
    });
    await alert.present();
}

postOrderCommunicationLog(orderId: number,message:string): void {
  let values:any={
    order_id:orderId,
    new_value:message,
    is_message:true,
    create_mail_message:this.createCommunicationLogMessage
    
  }
  this.odooRPC.call('rb_delivery.order', 'create_communication_log', [values])
    .catch(error => {
      console.error('Failed to post order communication log:', error);
    });
}
  callSender(orderId: number){
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{

      let buttons:ActionSheetButton[]=[]
      if(order.business_mobile_number || order.second_business_mobile_number){
        buttons.push(
          {
            text:this.translate.instant('CALL_VIA_CELL'),
            handler:(()=>{
              this.callSenderViaCell(orderId)
            }),
            icon:'call-outline'
          }
        )
      }

      if(order.business_whatsapp_mobile || order.business_second_whatsapp_mobile){
        buttons.push(
          {
            text:this.translate.instant('CALL_VIA_WHATSAPP'),
            handler:(()=>{
              this.callSenderViaWhatsapp(orderId)
            }),
            icon:'logo-whatsapp'
          }
        )
      }
      let header = this.translate.instant('CALL')
      let errorMessage = this.translate.instant('NO_MOBILE_NUMBER_FOUND')
      this.showActionSheet(header,buttons,errorMessage)
    })
  }

  callSenderViaCell(orderId: number) {
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      let buttons:ActionSheetButton[]=[]
      if(order.business_mobile_number){
        buttons.push(
          {
            text:order.business_mobile_number,
            handler:(()=>{
              this.postOrderCommunicationLog(orderId, this.translate.instant('CALL_SENDER')+' '+order.business_mobile_number+' '+this.translate.instant('VIA_CELL'));
              window.open("tel:"+order.business_mobile_number+"", '_blank');
            }),
            icon:'call-outline'
          }
        )
      }
      if(order.second_business_mobile_number){
        buttons.push(
          {
            text:order.second_business_mobile_number,
            handler:(()=>{
              this.postOrderCommunicationLog(orderId, this.translate.instant('CALL_SENDER')+' '+order.second_business_mobile_number+' '+this.translate.instant('VIA_CELL'));
              window.open("tel:"+order.second_business_mobile_number+"", '_blank');
            }),
            icon:'call-outline'
          }
        )
      }
      if(order.alt_mobile_number){
        buttons.push(
          {
            text:order.alt_mobile_number,
            handler:(()=>{
              this.postOrderCommunicationLog(orderId, this.translate.instant('CALL_SENDER')+' '+order.alt_mobile_number+' '+this.translate.instant('VIA_CELL'));
              window.open("tel:"+order.alt_mobile_number+"", '_blank');
            }),
            icon:'call-outline'
          }
        )
      }

      let header = this.translate.instant('CALL')
      let errorMessage = this.translate.instant('NO_MOBILE_NUMBER_FOUND')
      this.showActionSheet(header,buttons,errorMessage)
    })
  }
  
  callSenderViaWhatsapp(orderId: number) {
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      let buttons:ActionSheetButton[] = []
      if(order.business_whatsapp_mobile){
        buttons.push(
          {
            text:order.business_whatsapp_mobile,
            handler:(()=>{
              this.postOrderCommunicationLog(orderId, this.translate.instant('CALL_SENDER')+' '+order.business_whatsapp_mobile+' '+this.translate.instant('VIA_WHATSAPP'));
              window.open("https://wa.me/"+order.business_whatsapp_mobile, '_blank');
            }),
            icon:'logo-whatsapp'
          }
        )
      }
      if(order.business_second_whatsapp_mobile){
        buttons.push(
          {
            text:order.business_second_whatsapp_mobile,
            handler:(()=>{
              this.postOrderCommunicationLog(orderId, this.translate.instant('CALL_SENDER')+' '+order.business_second_whatsapp_mobile+' '+this.translate.instant('VIA_WHATSAPP'));
              window.open("https://wa.me/"+order.business_second_whatsapp_mobile, '_blank');
            }),
            icon:'logo-whatsapp'
          }
        )
      }
      let header = this.translate.instant('WHATSAPP_CALL')
      let errorMessage = this.translate.instant('NO_WHATSAPP_MOBILE_FOUND')
      this.showActionSheet(header,buttons,errorMessage)
    })
  }


  callAgent(orderId: number){
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{

      let buttons:ActionSheetButton[]=[]
      if(order.agent_mobile_number || order.second_agent_mobile_number){
        buttons.push(
          {
            text:this.translate.instant('CALL_VIA_CELL'),
            handler:(()=>{
              this.callAgentViaCell(orderId)
            }),
            icon:'call-outline'
          }
        )
      }

      if(order.agent_whatsapp_mobile || order.agent_second_whatsapp_mobile){
        buttons.push(
          {
            text:this.translate.instant('CALL_VIA_WHATSAPP'),
            handler:(()=>{
              this.callAgentViaWhatsapp(orderId)
            }),
            icon:'logo-whatsapp'
          }
        )
      }
      let header = this.translate.instant('CALL')
      let errorMessage = this.translate.instant('NO_MOBILE_NUMBER_FOUND')
      this.showActionSheet(header,buttons,errorMessage)
    })
  }

  callAgentViaCell(orderId: number) {
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      let buttons:ActionSheetButton[]=[]
      if(order.agent_mobile_number){
        buttons.push(
          {
            text:order.agent_mobile_number,
            handler:(()=>{
              this.postOrderCommunicationLog(orderId, this.translate.instant('CALL_AGENT')+' '+order.agent_mobile_number+' '+this.translate.instant('VIA_CELL'));

              window.open("tel:"+order.agent_mobile_number+"", '_blank');
            }),
            icon:'call-outline'
          }
        )
      }
      if(order.second_agent_mobile_number){
        buttons.push(
          {
            text:order.second_agent_mobile_number,
            handler:(()=>{
              this.postOrderCommunicationLog(orderId, this.translate.instant('CALL_AGENT')+' '+order.second_agent_mobile_number+' '+this.translate.instant('VIA_CELL'));
              window.open("tel:"+order.second_agent_mobile_number+"", '_blank');
            }),
            icon:'call-outline'
          }
        )
      }
      let header = this.translate.instant('CALL')
      let errorMessage = this.translate.instant('NO_MOBILE_NUMBER_FOUND')
      this.showActionSheet(header,buttons,errorMessage)
    })
  }

  getStatusFilterName() {
    if (this.statusDomain && this.statusDomain[0] && this.statusDomain[0][2]){
      this.statusFilterPlaceHolder = this.statusDomain[0][2]
    } else if (this.domain && this.domain[0] && this.domain[0][0] == 'state' && this.domain[0][2]) {
      this.statusFilterPlaceHolder =  this.domain[0][2]
    } else 
      this.statusFilterPlaceHolder =  'ALL'
    
  }
  
  callAgentViaWhatsapp(orderId: number) {
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      let buttons:ActionSheetButton[] = []
      if(order.agent_whatsapp_mobile){
        buttons.push(
          {
            text:order.agent_whatsapp_mobile,
            handler:(()=>{
              this.postOrderCommunicationLog(orderId, this.translate.instant('CALL_AGENT')+' '+order.agent_whatsapp_mobile+' '+this.translate.instant('VIA_WHATSAPP'));

              window.open("https://wa.me/"+order.agent_whatsapp_mobile, '_blank');
            }),
            icon:'logo-whatsapp'
          }
        )
      }
      if(order.agent_second_whatsapp_mobile){
        buttons.push(
          {
            text:order.agent_second_whatsapp_mobile,
            handler:(()=>{
              this.postOrderCommunicationLog(orderId, this.translate.instant('CALL_AGENT')+' '+order.agent_second_whatsapp_mobile+' '+this.translate.instant('VIA_WHATSAPP'));

              window.open("https://wa.me/"+order.agent_second_whatsapp_mobile, '_blank');
            }),
            icon:'logo-whatsapp'
          }
        )
      }
      let header = this.translate.instant('WHATSAPP_CALL')
      let errorMessage = this.translate.instant('NO_WHATSAPP_MOBILE_FOUND')
      this.showActionSheet(header,buttons,errorMessage)
    })
  }

  callCustomerViaCell(orderId: number) {
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      let buttons:ActionSheetButton[]=[]
      if(order.customer_mobile){
        buttons.push(
          {
            text:order.customer_mobile,
            handler:(()=>{
              this.postOrderCommunicationLog(orderId, this.translate.instant('CALL_CUSTOMER')+' '+order.customer_mobile+' '+this.translate.instant('VIA_CELL'));
              window.open("tel:"+order.customer_mobile+"", '_blank');
            }),
            icon:'call-outline'
          }
        )
      }
      if(order.second_mobile_number){
        buttons.push(
          {
            text:order.second_mobile_number,
            handler:(()=>{
              this.postOrderCommunicationLog(orderId, this.translate.instant('CALL_CUSTOMER')+' '+order.second_mobile_number+' '+this.translate.instant('VIA_CELL'));
              window.open("tel:"+order.second_mobile_number+"", '_blank');
            }),
            icon:'call-outline'
          }
        )
      }
      let header = this.translate.instant('CALL')
      let errorMessage = this.translate.instant('NO_MOBILE_NUMBER_FOUND')
      this.showActionSheet(header,buttons,errorMessage)
    })
  }
  callCustomerViaWhatsapp(orderId: number) {
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      let buttons:ActionSheetButton[] = []
      if(order.cus_whatsapp_mobile){
        buttons.push(
          {
            text:order.cus_whatsapp_mobile,
            handler:(()=>{
              this.postOrderCommunicationLog(orderId, this.translate.instant('CALL_CUSTOMER')+' '+order.cus_whatsapp_mobile+' '+this.translate.instant('VIA_WHATSAPP'));
              window.open("https://wa.me/"+order.cus_whatsapp_mobile, '_blank');
            }),
            icon:'logo-whatsapp'
          }
        )
      }
      if(order.cus_second_whatsapp_mobile){
        buttons.push(
          {
            text:order.cus_second_whatsapp_mobile,
            handler:(()=>{
              this.postOrderCommunicationLog(orderId, this.translate.instant('CALL_CUSTOMER')+' '+order.cus_second_whatsapp_mobile+' '+this.translate.instant('VIA_WHATSAPP'));
              window.open("https://wa.me/"+order.cus_second_whatsapp_mobile, '_blank');
            }),
            icon:'logo-whatsapp'
          }
        )
      }
      let header = this.translate.instant('WHATSAPP_CALL')
      let errorMessage = this.translate.instant('NO_WHATSAPP_MOBILE_FOUND')
      this.showActionSheet(header,buttons,errorMessage)
    })
  }

  messageCustomer(orderId:number){
      let buttons:ActionSheetButton[] = []
      buttons.push(
        {
          text:this.translate.instant('MESSAGE_CUSTOMER_VIA_WHATSAPP'),
          handler:(()=>{
            this.postOrderCommunicationLog(orderId,'MESSAGE_CUSTOMER_VIA_WHATSAPP');
            this.messageCustomerViaWhatsapp(orderId);
          }),
          icon:'logo-whatsapp'
        }
      )
      buttons.push(
        {
          text:this.translate.instant('MESSAGE_CUSTOMER_VIA_SMS'),
          handler:(()=>{
            this.postOrderCommunicationLog(orderId,'MESSAGE_CUSTOMER_VIA_SMS');
            this.messageCustomerViaSms(orderId);
          }),
          icon:'chatbubble-ellipses-outline'
        }
      )
      let header = this.translate.instant('MESSAGE_CUSTOMER')
      let errorMessage = this.translate.instant('NO_MESSAGE_FOUND')
      this.showActionSheet(header,buttons,errorMessage)
  }


  messageCustomerViaSms(orderId: number) {
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      let buttons:ActionSheetButton[] = []
      this.odooRPC.call('rb_delivery.order','get_sms_messages',[orderId]).then(smsMessages =>{
        if(smsMessages && smsMessages.body.result.result.length > 0){
          let formatedMessages = smsMessages.body.result.result
          if ([false, undefined].includes(order.cus_whatsapp_mobile as any) && [false, undefined].includes(order.cus_second_whatsapp_mobile as any)) {
            this.dialogService.error({
              title: this.translate.instant('ERROR_WHILE_SENDING_SMS_MESSAGE'),
              message: this.translate.instant('NO_SMS_MOBILE_NUMBER_PROVIDED'),
              whatToDo: this.translate.instant('PLEASE_MAKE_SURE_TO_PROVIDE_SMS_MOBILE_NUMBER_IF_ITS_PROVIDED_PLESAE_CONTACT_SUPPORT'),
              code: '1234',
            })
            return
          }
          if(formatedMessages && formatedMessages.length > 0){
            for(let message of formatedMessages){
              buttons.push(
                {
                  text:message,
                  handler:(()=>{
                    this.openSmsMessageSheet(order,[order.customer_mobile,order.second_mobile_number],message)
                  }),
                  icon:'chatbubble-ellipses-outline'
                }
              )
            }
          }
        }
        let header = this.translate.instant('SMS_MESSAGE')
        let errorMessage = this.translate.instant('NO_SMS_MESSAGE_FOUND')
        this.showActionSheet(header,buttons,errorMessage)
      })
    })
  }

  messageSender(orderId:number){

    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      let buttons:ActionSheetButton[] = []
      buttons.push(
        {
          text:this.translate.instant('MESSAGE_SENDER_VIA_WHATSAPP'),
          handler:(()=>{
            this.postOrderCommunicationLog(orderId,'MESSAGE_SENDER_VIA_WHATSAPP');
            this.messageSenderFunction(orderId, 'whatsapp')
          }),
          icon:'logo-whatsapp'
        }
      )
      buttons.push(
        {
          text:this.translate.instant('MESSAGE_SENDER_VIA_SMS'),
          handler:(()=>{
            this.postOrderCommunicationLog(orderId,'MESSAGE_SENDER_VIA_SMS');
            this.messageSenderFunction(orderId, 'sms')
          }),
          icon:'chatbubble-ellipses-outline'
        }
      )
      let header = this.translate.instant('MESSAGE_SENDER')
      let errorMessage = this.translate.instant('NO_MESSAGE_FOUND')
      this.showActionSheet(header,buttons,errorMessage)
    })
  }

  messageSenderFunction(orderId: number, type: 'whatsapp' | 'sms') {
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order => {
      const isWhatsapp = type === 'whatsapp';
      
      if ((
          isWhatsapp && [false, undefined].includes(order.business_whatsapp_mobile as any) &&
          [false, undefined].includes(order.business_second_whatsapp_mobile as any)) 
          || !isWhatsapp && 
          [false, undefined].includes(order.business_mobile_number as any) &&
          [false, undefined].includes(order.second_business_mobile_number as any) &&
          [false, undefined].includes(order.alt_mobile_number as any)) {
        this.dialogService.error({
          title: this.translate.instant(isWhatsapp ? 'ERROR_WHILE_SENDING_WHATSAPP_MESSAGE' : 'ERROR_WHILE_SENDING_SMS_MESSAGE'),
          message: this.translate.instant(isWhatsapp ? 'NO_WHATSAPP_MOBILE_NUMBER_PROVIDED' : 'NO_SMS_MOBILE_NUMBER_PROVIDED'),
          whatToDo: this.translate.instant(
            isWhatsapp ?
              'PLEASE_MAKE_SURE_TO_PROVIDE_WHATSAPP_MOBILE_NUMBER_IF_ITS_PROVIDED_PLESAE_CONTACT_SUPPORT' :
              'PLEASE_MAKE_SURE_TO_PROVIDE_SMS_MOBILE_NUMBER_IF_ITS_PROVIDED_PLESAE_CONTACT_SUPPORT'
          ),
          code: '1234',
        });
        return;
      } 
      
      this.odooRPC.call('rb_delivery.order', 'get_sender_messages', [orderId, isWhatsapp])
        .then(response => {
          const messages = response?.body?.result?.result;
          const hasMessages = messages && messages.length > 0;
          if (hasMessages) {
            let buttons: ActionSheetButton[] = [];
            for (let message of messages) {
              buttons.push({
                text: message,
                handler: isWhatsapp ? async () => {
                  try {
                    await navigator.clipboard.writeText(message);
                    const toast = await this.toastCtrl.create({
                      message: this.translate.instant('MESSAGE_COPIED_SUCCESSFULLY'),
                      duration: 3000,
                      position: 'top'
                    });
                    toast.present();
                  } catch (err) {
                    console.error('Failed to copy message:', err);
                  }
                  this.openWhatsAppMessageSheet(
                    order,
                    [order.business_whatsapp_mobile,
                    order.business_second_whatsapp_mobile, order.alt_mobile_number],
                    message
                  );
                } : () => {
                  this.openSmsMessageSheet(
                    order,
                    [order.business_mobile_number,
                    order.second_business_mobile_number, order.alt_mobile_number],
                    message
                  );
                },
                icon: isWhatsapp ? 'logo-whatsapp' : 'chatbubble-ellipses-outline'
              });
            }
            const header = this.translate.instant(isWhatsapp ? 'MESSAGE_SENDER' : 'SMS_MESSAGE');
            const errorMessage = this.translate.instant(isWhatsapp ? 'NO_MESSAGE_FOUND' : 'NO_SMS_MESSAGE_FOUND');
            this.showActionSheet(header, buttons, errorMessage);
          } else {
            if (isWhatsapp) {
              this.openWhatsAppMessageSheet(
                order,
                [order.business_whatsapp_mobile,
                order.business_second_whatsapp_mobile, order.alt_mobile_number],
                ''
              );
            } else {
              this.openSmsMessageSheet(
                order,
                [order.business_mobile_number,
                order.second_business_mobile_number, order.alt_mobile_number],
                ''
              );
            }
          }
        });
    });
  }
  

  messageAgent(orderId:number){

    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      let buttons:ActionSheetButton[] = []
      buttons.push(
        {
          text:this.translate.instant('MESSAGE_AGENT_VIA_WHATSAPP'),
          handler:(()=>{
            this.postOrderCommunicationLog(orderId,'MESSAGE_AGENT_VIA_WHATSAPP');

            this.openWhatsAppMessageSheet(order,[order.agent_whatsapp_mobile,order.agent_second_whatsapp_mobile],'')
          }),
          icon:'logo-whatsapp'
        }
      )
      buttons.push(
        {
          text:this.translate.instant('MESSAGE_AGENT_VIA_SMS'),
          handler:(()=>{
            this.postOrderCommunicationLog(orderId,'MESSAGE_SENDER_VIA_SMS');
            this.openSmsMessageSheet(order,[order.agent_mobile_number,order.second_agent_mobile_number],'')
          }),
          icon:'chatbubble-ellipses-outline'
        }
      )
      let header = this.translate.instant('MESSAGE_AGNET')
      let errorMessage = this.translate.instant('NO_MESSAGE_FOUND')
      this.showActionSheet(header,buttons,errorMessage)
    })
  }


  openSmsMessageSheet(orderId:any,mobileNumbers:string[],message:string){
    let buttons:ActionSheetButton[] = []
    for (let mobileNumber of mobileNumbers) {
      if (!mobileNumber) continue;
      buttons.push(
        {
          text:mobileNumber,
          handler:(()=>{
            this.postOrderCommunicationLog(orderId.id, this.translate.instant('MESSAGE')+' '+mobileNumber+' '+this.translate.instant('VIA_SMS'));
            window.open("sms://"+mobileNumber+"/&body="+message, '_blank');
          }),
          icon:'chatbubble-ellipses-outline'
        }
      )
    }
    let header = this.translate.instant('SMS_MESSAGE')
    let errorMessage = this.translate.instant('NO_SMS_MOBILE_FOUND')
    this.showActionSheet(header,buttons,errorMessage)
  }

  messageCustomerViaWhatsapp(orderId: number) {
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      let buttons:ActionSheetButton[] = []
      this.odooRPC.call('rb_delivery.order','get_whatsapp_messages',[orderId]).then(whatsappMessages=>{
        if(whatsappMessages && whatsappMessages.body.result.result.length > 0){
          let formatedMessages = whatsappMessages.body.result.result
          if ([false, undefined].includes(order.cus_whatsapp_mobile as any) && [false, undefined].includes(order.cus_second_whatsapp_mobile as any)) {
            this.dialogService.error({
              title: this.translate.instant('ERROR_WHILE_SENDING_WHATSAPP_MESSAGE'),
              message: this.translate.instant('NO_WHATSAPP_MOBILE_NUMBER_PROVIDED'),
              whatToDo: this.translate.instant('PLEASE_MAKE_SURE_TO_PROVIDE_WHATSAPP_MOBILE_NUMBER_IF_ITS_PROVIDED_PLESAE_CONTACT_SUPPORT'),
              code: '1234',
            })
            return
          }
          if(formatedMessages && formatedMessages.length > 0){
          for(let message of formatedMessages){
              buttons.push(
                {
                  text:message,
                  handler:(()=>{
                    this.openWhatsAppMessageSheet(orderId,[order.cus_whatsapp_mobile,order.cus_second_whatsapp_mobile],message)
                  }),
                  icon:'logo-whatsapp'
                }
              )
            }
          }
        }
        let header = this.translate.instant('WHATSAPP_MESSAGE')
        let errorMessage = this.translate.instant('NO_WHATSAPP_MESSAGE_FOUND')
        this.showActionSheet(header,buttons,errorMessage)
      })
    })
  }

  openWhatsAppMessageSheet(orderId:any,mobileNumbers:string[],message:string){
    let buttons:ActionSheetButton[] = []
    for (let mobileNumber of mobileNumbers) {
      if (!mobileNumber) continue;
      buttons.push(
        {
          text:mobileNumber,
          handler:(async ()=>{
            this.postOrderCommunicationLog(orderId.id, this.translate.instant('SEND_WHATSAPP')+' '+mobileNumber+' '+this.translate.instant('VIA_WHATSAPP'));
            setTimeout(() => {
              window.open("https://wa.me/" + mobileNumber + "?text=" + encodeURIComponent(message));
            }, 0);
          }),
          icon:'logo-whatsapp'
        }
      )
    }
    let header = this.translate.instant('WHATSAPP_MESSAGE')
    let errorMessage = this.translate.instant('NO_WHATSAPP_MOBILE_FOUND')
    this.showActionSheet(header,buttons,errorMessage)
  }

  showActionSheet(header: string, buttons: ActionSheetButton[],errorMessage:string) {
    if(buttons.length>1){
      this.actionSheetCtrl.create({
        mode:'md',
        header:header,
        buttons:buttons
      }).then(actionSheet=>actionSheet.present())
    }
    else if(buttons.length==1 && buttons[0].handler){
      buttons[0].handler()
    }
    else{
      this.dialogService.error({
        header: header,
        message: errorMessage,
        code: '1205',
      })
    }
  }
  printOrder =  (orderId: number) => {
    this.selectedOrderIds=[orderId]
    this.fetchReports()
  }
  goToClonerOrder(orderId: number) {
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      let domain=[['id','=',order.cloner_order_id[0]]]
      this.infiniteScrollDisabled=true
      this.orderStore.dispatch(new orderActions.LoadHttp({search:domain,offset:0,addToTempOrders:true, limit:0}))
      combineLatest([
        this.orderStore.select(orderSelectors.selectTempStructuredOrderByIds([order.cloner_order_id[0]])),
        this.orderStore.select(orderSelectors.selectLoading)
      ])
      .pipe(filter(([,loading])=>!loading),take(1)).subscribe(([orderStructures,])=>{
        this.openOrderRecordModal(orderStructures,0)
      })
    })
  }
  goToClonedOrders(orderId: number) {
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      let domain=[['id','in',order.cloned_order_ids]]
      this.infiniteScrollDisabled=true
      this.orderStore.dispatch(new orderActions.LoadHttp({search:domain,offset:0,addToTempOrders:true, limit:0}))
      combineLatest([
        this.orderStore.select(orderSelectors.selectTempStructuredOrderByIds(order.cloned_order_ids)),
        this.orderStore.select(orderSelectors.selectLoading)
      ])
      .pipe(filter(([,loading])=>!loading),take(1)).subscribe(([orderStructures,])=>{
        this.openOrderRecordModal(orderStructures,0)
      })
    })
  }
  openOrderRecordModal(orderStructures: RecordCardStructure[], index: number) {
    this.infiniteScrollDisabled=false
    let buttonClickDetector = new Subject<any>();
    let destroyed = new Subject<any>();
    this.modalCtrl.create({
      component:RecordItemComponent,
      cssClass:"windowed-modal fit-window",
      componentProps:{
        recordCardStructure:orderStructures[index],
        structuresList:orderStructures,
        removeMargin:true,
        buttonClickDetector:buttonClickDetector
      }
    }).then(modal=>{
      buttonClickDetector.pipe(takeUntil(destroyed)).subscribe(event=>{
        this.doFunction(event.functionName,event.orderId)
      })
      modal.present()
      modal.onDidDismiss().then(()=>{
        destroyed.next({})
        destroyed.unsubscribe()
        buttonClickDetector.unsubscribe()
      })
    })
  }
  editOrder(orderId:number,disableEdit=false){
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      this.modalCtrl.create({
        component: RecordCreatorPage,
        componentProps: {
          formName: 'order_form',
          field: {
            'name': this.translate.instant('ORDER')
          },
          disableEdit,
          record:order
        }
      }).then(modal => {
        modal.present()
      })
    })
  }

  showOrder(orderId:number,disableEdit=false){
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      this.modalCtrl.create({
        component: OrderDetailComponent,
        componentProps: {
          record:order,
          doPrint: this.printOrder,
          doFunction: this.doFunction
        }
      }).then(modal => {
        modal.present()
      })
    })
  }

  showMoreDetailsMovify(orderId:number){
    this.showOrder(orderId,true)
  }

  async showAttachment(orderId: number): Promise<void> {
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(async (order) => {
      const modal = await this.modalCtrl.create({
        component: AttachmentModal,
        cssClass:"showattchment",
        componentProps: {
          attachmentIds: order.attachment_ids  
        }
      });
  
      await modal.present();
    });
  }
  


  showMoreDetails(orderId:number){
    this.editOrder(orderId,true)
  }

  changeStatus(orderId: number) {
    this.presentStatusAlert([orderId]);
}

ionViewDidEnter(){
  if(this.searchValue){
    this.searchControl.setValue(this.searchValue)
  }
}


initSearch(): void {
    this.searchSubscription = this.searchControl.valueChanges.pipe(
      debounceTime(1000),
      distinctUntilChanged()
    )
    .subscribe(value => {
      if(value != null){
        this.searchValue = value
        this.performSearch(this.searchValue)
      }
    });
    this.getDefaultSearchFields();
}



getDefaultSearchFields(){
this.orderStore.select(orderSelectors.selectDefaultSearchFilter).subscribe(fields => {
  if(fields.length>0){
    this.defaultSearchFields = fields;
  }
  else{
    this.defaultSearchFields=['sequence']
  }
});
}

getFilterFields(){
  this.orderStore.select(orderSelectors.selectFilterFields).subscribe(fields => {
    this.filterFields = Object.values(fields);
  });
}

getGroupByFields(){
  this.orderStore.select(orderSelectors.selectGroupByFields).subscribe(fields => {
    this.groupByFields = Object.values(fields);
  });
  this.storage.get('user_info').then(userInfo=>{
    if(userInfo){
      this.odooRPC.call('rb_delivery.group_by_card_configurations', 'get_group_by_card', ['rb_delivery.order', userInfo[0].group_id[0]]).then(async response=>{
        if (response&&response.body&&response.body.result&&response.body.result.result) {
          this.groupByCardFields=response.body.result.result
        }
      })
    }
  })
}

getOrderSlots(){
  this.orderStore.dispatch(new orderActions.LoadOrderSlots({}))
  this.orderStore.select(orderSelectors.selectOrderSlots).subscribe(slots => {  
    this.orderSlots = slots;
  });
  
  
}

generateSearchDomain(fields: string[], search: string): any[] {
const domain = [];

for (let i = 0; i < fields.length - 1; i++) {
    domain.push('|');
}

for (const field of fields) {
    domain.push([field, 'ilike', search]);
}

return domain;
}


private performSearch(value: string): void {
  this.searchDomain=[]
  if(value){
    const valueToSearch = value.toLowerCase();
    const searchFields = this.selectedFilterField ? [this.selectedFilterField] : this.defaultSearchFields;
    this.searchDomain = this.generateSearchDomain(searchFields, valueToSearch);
  }
  this.updateOrdersSearch(this.neededFields)
}

async getDomain(): Promise<any[]> {
  if (!this.isFiltered) {
    const orderFilter = await this.modelsFilterStore.select(modelsFilterSelectors.selectModelFilterByModelName('rb_delivery.order')).pipe(take(1)).toPromise();
    if (orderFilter.length > 0) {
      return this.domain.concat(this.groupByDomain, this.searchDomain, this.dateDomain, this.statusDomain, orderFilter[0].domain);
    } else {
      return this.domain.concat(this.groupByDomain, this.searchDomain, this.dateDomain, this.statusDomain);
    }
  } else {
    return this.domain.concat(this.groupByDomain, this.searchDomain, this.dateDomain, this.statusDomain);
  }
}

clearSearchFilter(){
  this.selectedFilterField=''
  this.searchControl.setValue('')
  this.dateDomain=[]
  this.searchFilterPlaceholder = 'ALL'
  this.statusFilterPlaceHolder = 'ALL'
  this.searchValue = ''
  this.selectedDateFilter = null
  this.updateOrdersSearch()
  this.getStatusFilterName()

}

handleRouteReorder(event:ItemReorderCustomEvent){
  this.reordering = true
  let fromIndex = event.detail.from
  let toIndex = event.detail.to
  let orderIds: number[] = this.orderIds;
  const element = orderIds.splice(fromIndex, 1)[0];
  orderIds.splice(toIndex, 0, element);
  this.orderStore.dispatch(new orderActions.ResequenceHttp({orderIds:orderIds,field:'route_sequence',offset:1}))
  this.orderStore.select(orderSelectors.selectReordering).pipe(filter(loading=>!loading),take(1)).subscribe(()=>{
      this.handleRefresh(event as any,true)
    this.reordering = false
  })
}


async showFilterOptions() {
  const inputs = this.filterFields.map((field: any) => {
      return {
          name: field.name,
          type: 'radio',
          label: this.translate.instant(field.description),
          value: field.name,
          checked: this.selectedFilterField === field.name
      };
  });
  const alert = await this.alertController.create({
      header: this.translate.instant('SELECT_FEILDS_TO_SEARCH'),
      inputs: inputs.map((input: any, index: number) => ({
        ...input,
          checked: index === 0
      })),
      buttons: [
          {
              text: this.translate.instant('CANCEL'),
              role: this.translate.instant('CANCEL')
          },
          {
              text:this.translate.instant('Ok'),
              handler: (selectedValue) => {
                  const selectedField = this.filterFields.find((field: any) => field.name === selectedValue);
                  this.searchFilterPlaceholder = this.translate.instant(selectedField.description);
                  this.selectedFilterField = selectedField.name
                  this.searchControl.reset()
              }
          }
      ],
      mode: 'ios',
  });
  await alert.present();
}
  async openGroupingOrdersAlert() {
    const groupedOrdersAlert = await this.alertController.create({
      header: this.translate.instant('GROUP_BY'),
      inputs: this.groupByFields.map((field: any) => {
        return {
            name: field.description,
            type: 'radio',
            label: this.translate.instant(field.description),
            value: field,
            checked: this.selectedGroupOption === field.name
        };
      }),
      buttons: [
        {
          text: this.translate.instant('CANCEL'),
          role: 'cancel',
        },
        {
          text: this.translate.instant('OK'),
          handler: async (selectedOption) => {
            this.selectedOption = selectedOption;
            this.groupOptionLabels.push(groupedOrdersAlert.inputs.filter(input=>input.value==selectedOption)[0].label as string) 
            let groupFields = [this.selectedOption.name.split(':')[0]]
            if (this.groupByCardFields) {
              let fields = this.groupByCardFields.map((item: { name: any; }) => item.name)
              groupFields = [...groupFields, ...fields];
            }
            this.orderStore.dispatch(new orderActions.LoadOrdersGroupedBy({ domain: await this.getDomain(), fields: groupFields, groupBy: [this.selectedOption.name] }))
            this.groupOrdersByOption(selectedOption);
            this.groupByFields = this.groupByFields.filter((item: { name: string; }) => item.name !== selectedOption.name);
          },
        },
      ],
      mode: 'ios',
      animated: true
    });
    await groupedOrdersAlert.present();
  }
  groupOrdersByOption(selectedOption: {name:string,discription:string}) {
    this.selectedGroupOption = this.groupByFields.filter((field:any)=>selectedOption.name==field.name)[0].description
    this.isGroupByEnabled = true;
    this.groupedOrders = [];
    combineLatest([
      this.orderStore.select(selectGroupedOrders),
      this.orderStore.select(orderSelectors.selectLoading)
    ])
    .pipe(filter(([groups,loading])=>!loading),take(1)).subscribe(([groupedOrders,loading]) => {
      if (groupedOrders && groupedOrders.length > 0) {
        this.groupedOrders = groupedOrders.map((groupedOrder: any) => ({
            key: Array.isArray(groupedOrder[selectedOption.name]) ? groupedOrder[selectedOption.name][1] : groupedOrder[selectedOption.name],
            keyId: Array.isArray(groupedOrder[selectedOption.name]) ? groupedOrder[selectedOption.name][0] : null,
            recordsLength: groupedOrder[`${selectedOption.name.split(':')[0]}_count`],
            type: 'groupedOrders',
            otherKeys: this.getRestOfFields(selectedOption, groupedOrder),
            domain:groupedOrder['__domain'],
            fieldName:selectedOption.name.split(':')[0],
            groupBy:selectedOption.name
        }));
        for(let i=0; i<this.groupedOrders.length; i++){
          this.groupedOrders[i]['structure'] = this.recordStructreService.formatForGroup(this.groupedOrders[i])
        }
      }
    })
  }

  getRestOfFields(selectedOption:any, groupedOrder:any) {
    let rows:any = []
    for (const key in groupedOrder) {
      if (groupedOrder.hasOwnProperty(key) && key != '__domain' && this.groupByCardFields) {
        const result = this.groupByCardFields.find((item: { name: string; }) => item.name === key)
        if (result) {
          rows.push({
            'name':result['field_description'],
            'value':groupedOrder[key]
          })
        }
      }
    }
    return rows
  }


  openGroupedOrders(group: IGroup) {
    this.selectedGroups.push(group)
    let dateGroupsLength = this.selectedGroups.filter(g=>g.groupBy.includes(':')).length
    this.isScrolling=false
    this.isGroupByEnabled = false
    this.isGroupOpened = true
    this.groupByDomain = this.groupByDomain.concat(group.domain)
    this.groupByDomain = this.groupByDomain
        .filter((domain: any) => domain !== '&' && domain !== '|')
        .filter((domain: any[], idx: number, self: any[]) => {
            return (
                idx === self.findIndex(d => d[0] === domain[0] && d[1] === domain[1] && d[2] === domain[2] )
            );
        });
    this.groupByDomain = this.groupByDomain.slice(0,this.selectedGroups.length+dateGroupsLength)
    this.groupingLevels.push(this.translate.instant(group.key || "UNDEFINED"));
    this.updateOrdersSearch()
    
  }
  closeGroupedOrders(index:number) {
    this.getGroupByFields()
    let removedGroup = this.selectedGroups.splice(index, 1);
    this.groupingLevels.splice(index, 1);
    this.groupOptionLabels.splice(index, 1);

    this.groupByDomain = this.groupByDomain
        .filter((domain: any[]) => {
            // Remove duplicates based on domain field name
            return (
                domain[0] !== removedGroup[0].fieldName
            );
        });

    let fields: string[] = [];
    this.groupByDomain = this.groupByDomain.filter((domain: any[]) => {
        if (domain[0] !== removedGroup[0].fieldName) {
            fields.push(domain[0]);
            return true;
        } else {
            return false;
        }
    });

    this.groupByFields = this.groupByFields.filter(
        (item: { name: string }) => !fields.includes(item.name.split(":")[0])
    );

    this.updateOrdersSearch();

    if (this.selectedGroups.length === 0) {
        this.closeGroupBy();
    }

  }

  closeGroupBy() {
    this.isGroupOpened = false;
    this.isGroupByEnabled = false;
    this.groupByDomain = []
    this.groupingLevels= []
    this.groupOptionLabels =[]
    this.getGroupByFields()
    this.updateOrdersSearch()
  }
  selectOrdersItems(){
    this.isSelectEnabled = !this.isSelectEnabled
    if(!this.isSelectEnabled){
      this.selectedOrderIds=[]
    }
  }
  checkSelected(orderId : number){
    if(this.selectedOrderIds.includes(orderId)){
      return true
    }
    else{
      return false
    }
  }
  selectOrder(orderId : number,index:number){
    if (!this.selectedOrderIds.includes(orderId)){
      this.selectedOrderIds.push(orderId)
    }
    else{
      this.selectedOrderIds.splice(this.selectedOrderIds.indexOf(orderId),1)
    }
    if(this.selectedOrderIds.length>0){
      // to get the lowest index of the selectedOrderIds inside the orderIds
      this.firstSelectedOrderOffset=this.selectedOrderIds.map((id) => ({ id: id, index: this.orderIds.indexOf(id) }))
      .filter((item) => item.index !== -1)
      .sort((a, b) => a.index - b.index)[0].index +1;
    }
    else{
      this.firstSelectedOrderOffset=undefined
    }
  }
  filterByStatus(event:SelectCustomEvent) {
    this.clearFilter();
    this.isChangingStatusFilter = true
    this.statusDomain=[]
    if (event && event.detail && event.detail.value && event.detail.value) {
      const value = event.detail.value;
      if (this.loadIntoTempOrders) {
        this.tempDomain.forEach(element => {
            this.searchDomain.push(element)
        });
      } 
      this.statusDomain=[['state','=',value]]
    }
    this.getStatusFilterName()
    this.updateOrdersSearch()
  }
  async updateOrdersSearch(fields?:any[],sort?:string) {
    let searchParams:any={search: await this.getDomain()}
    if (!this.loadIntoTempOrders) {
      if(fields){
        searchParams['fields']=fields
      }
      if(sort){
        if(sort.includes('route_sequence')){
           this.showReordering = true
        }
        else{
          this.showReordering = false
          if(this.isRouteReorderEnabled){
            this.isRouteReorderEnabled=!this.isRouteReorderEnabled
          }
        }
        searchParams['sort']=sort
      }
      else{
        searchParams['sort']='create_date DESC'
      }
  
      this.orderStore.dispatch(new orderActions.UpdateSearch(searchParams));
      this.getCountOfOrders()
    } else {
      this.infiniteScrollDisabled = true;
      let searchDomain = this.objService.deepClone(this.tempDomain)
      this.statusDomain.forEach((domain) => {
          searchDomain.push(domain)
      })
      console.log(searchDomain)
      this.orderStore.dispatch(new orderActions.LoadHttp({search:searchDomain,offset:0,addToTempOrders:true,limit:0}))
      combineLatest([
        this.orderStore.select(orderSelectors.selectAllTempDataStructures),
        this.orderStore.select(orderSelectors.selectLoading)
      ])
      .pipe(filter(([orders,loading])=>!loading),take(1)).subscribe(([orderStructures,])=>{
        this.orderCardsStructures$ = of(orderStructures)
        this.getCountOfOrders()

        
        

      })
    }
  }
  async selectAllOrders(){
    await this.alertController.create({
      message:this.translate.instant("LOADED_ORDERS",{loadedOrders:this.loadedOrdersCount,allOrders:this.ordersCount}),
      backdropDismiss:false,
      cssClass:"alert-message",
      mode:"ios",
      buttons:[
        {
          text:this.translate.instant("SELECT_LOADED",{value:this.loadedOrdersCount }),
          handler: ()=>{
            this.orderStore.select(orderSelectors.selectAllIDS).pipe(filter(data => data && data.length > 0),take(1)).subscribe(loadedOrdersIds=>{
              if(loadedOrdersIds && loadedOrdersIds.length > 0){
                this.selectedOrderIds = loadedOrdersIds
              }
            })
          },
        },
        {
          text:this.translate.instant(this.ordersCount<=1000?"LOAD_AND_SELECT_ALL":"LOAD_AND_SELECT_1000",{value:this.ordersCount }),
          handler: ()=>{
            this.orderStore.dispatch(new orderActions.LoadHttp({limit:1000}))
            this.orderStore.select(orderSelectors.selectAllIDS).pipe(skip(1),take(1)).subscribe(orderIds=>{
              this.selectedOrderIds=orderIds
            })
          },
        },
        {
          text: this.translate.instant("CANCEL"),
          role: 'cancel',
        }
    ]
    }).then(alert=>alert.present())
  }
  fetchActions(){
    this.actionService.fetchActions('rb_delivery.order').then(orderActions =>{
      if(orderActions && orderActions.length > 0){
        this.orderActions = orderActions
        this.hideSelection = false
      }
      else{
        this.hideSelection = true
      }
    })
  }
  async selectAction(event : any){
    this.actionService.fetchActionInfo(event.target.value).then(info =>{
      switch (event.target.value) {
        case 'change_status':
          return this.fetchOrderStatus()
        case 'print':
          return this.fetchReports()
        case 'assign_agent':
          return this.openAgentSelector()
        case 'route_optimization':
          return this.optimizeRoute()
        case 'create_money_collection':
          return this.createCollection('moneyCollection')
        case 'create_agent_collection':
          return this.createCollection('agentCollection')
        case 'send_orders_through_vhub':
          return this.sendToVhub(info)
        default:
          return false
      }
    })
  }
  async sendToVhub(info: any) {
    if (info.company_user) {
      let response = await this.odooRPC.call('rb_delivery.select_company_user', 'get_orders', [[],{'active_ids':this.selectedOrderIds}, false, info.company_user])
      if (response && response.body && response.body.result && response.body.result.success) {
        this.dialogService.success({
          title: this.translate.instant('VHUB_ORDER_SENT_SUCCESS'),
          message: this.translate.instant("SUCCESS_SENDING_ORDER_TO_DELIVERY_COMPANY_PLEASE_CHECK_VHUB_TRANSACTION_LOGS_FOR_MORE_INFO"), 
          code: "1630", 
        })
      } else if (response && response.body && response.body.error && response.body.error.data && response.body.error.data.message) {
        this.dialogService.error({
          input: response.body.error.data.message
        })
      }
    }
  }

  
   async createCollection(name: string) {
    this.selectedOrderIds
    
    switch (name) {
      case 'moneyCollection':
        let context_business = {
          'order_ids': this.selectedOrderIds,
          'context_type': 'business'
        }

        this.modalCtrl.create({
          component:CreateCollectionConfirmationComponent,
          componentProps: {
            title: 'CREATE_MONEY_COLLECION',
            message: this.translate.instant('ARE_YOU_SURE_YOU_WANT_TO_CREATE_MONEY_COLLECTION_WITH') + this.selectedOrderIds.length + this.translate.instant('ORDERS'),
          },
          cssClass: "errors-dialog",
          mode:'ios',
        }).then(modal=>{
          modal.onDidDismiss().then(async output=>{
            if (output.data.accepted) {
              let result = await this.odooRPC.call('rb_delivery.utility', 'create_money_collection', [context_business])
              if (result && result.body && result.body.result && result.body.result.result) {
                let collectionIds = result.body.result.result
                this.modalCtrl.create({
                  component: SuccessCreateCollectionComponent,
                  componentProps: {
                    model: 'rb_delivery.multi_print_orders_money_collector',
                    cardName: 'money_collection_card',
                    ids: collectionIds,
                    collectionType: 'collection'
                  }, 
                  mode:'ios',
                  cssClass:'success-request-collection-modal',
                }).then(modal=>{
                  modal.present()
                })
              }else {
                this.dialogService.error({
                  input: result.body.error.data.message
                })
              }
            }
          })
          modal.present()
        })
        
        break;
      case 'agentCollection':

        this.modalCtrl.create({
          component:CreateCollectionConfirmationComponent,
          componentProps: {
            title: 'CREATE_AGENT_MONEY_COLLECION',
            message: this.translate.instant('ARE_YOU_SURE_YOU_WANT_TO_CREATE_AGENT_MONEY_COLLECTION_WITH') + this.selectedOrderIds.length + this.translate.instant('ORDERS'),
          },
          cssClass: "errors-dialog",
          mode:'ios',
        }).then(modal=>{
          modal.onDidDismiss().then(async output=>{
            if (output.data.accepted) {
              let result = await this.odooRPC.call('rb_delivery.utility', 'create_agent_collection', [[],this.selectedOrderIds, 'agent'])
              if (result && result.body && result.body.result && result.body.result.result) {
                let collectionIds = result.body.result.result
                this.modalCtrl.create({
                  component: SuccessCreateCollectionComponent,
                  componentProps: {
                    model: 'rb_delivery.agent_money_collection',
                    cardName: 'agent_money_collection_card',
                    ids: collectionIds,
                    collectionType: 'agent_collection'
                  }, 
                  mode:'ios',
                  cssClass:'success-request-collection-modal',
                }).then(modal=>{
                  modal.present()
                })
              } else {
                this.dialogService.error({
                  input: result.body.error.data.message
                })
              }
            }
          })
          modal.present()
        })
        break
    }
  }

  openAgentSelector() {
    this.modalCtrl.create({
      component:DynamicSelectionComponent,
      initialBreakpoint : 0.65,
      breakpoints :[0, 0.25, 0.5, 0.75,1],
      handleBehavior : 'cycle',
      componentProps: {
        modelName: 'rb_delivery.user',
        domain: [['group_id.code','=','rb_delivery.role_driver'],['state','in',['confirmed', 'reconfirmed']]],
        searchDomain:[['username','ilike','value'],['email','ilike','value'],['mobile_number','ilike','value']],
        limitPerSearch:20,
        selectionType: 'many2one' ,
        placeholder:this.translate.instant('AGENT'),
        imageFieldName:'user_image',
      }
      
    }).then(modal=>{
      modal.onDidDismiss().then(output=>{
        this.orderStore.dispatch(new orderActions.UpdateBulkHttp({'ids':this.selectedOrderIds,values:{'assign_to_agent':output.data.id}}))
        this.selectedOrderIds=[]
      })
      modal.present()
    })
  }

  showOrderHistory(orderId:number){
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      this.odooRPC.callRequest({args:[orderId],model:"rb_delivery.order",method:"get_status_history"}).then(async response=>{
        let data = response.body.result.result
        let historyList = await data.map((el:any)=>
          
         {
          return {
            create_uid:el.create_uid,
            new_state: el.new_value,
            old_state:el.old_value,
            creation_date: el.create_date,
            is_message:el.is_message
          }
  
        })
  
       
        const modal = await this.modalCtrl.create({
          component: OrderHistoryComponent,
          componentProps: {
            items: historyList,
            order: order,
          }
    
        });
    
        return await modal.present();
       
      })
    })
    
  }

  fetchOrderStatus() {
    this.presentStatusAlert(this.selectedOrderIds, true);
  }
  fetchReports(){
    this.printService.fetchReports('rb_delivery.order',this.userInfo[0].group_id[0]).then(orderReports =>{
      if(orderReports && orderReports.length > 0){
        this.displayReports(orderReports)
      }
      else{
        this.dialogService.error({
          message: this.translate.instant("NO_REPORTS_WHERE_FOUND"),
          code: '1403',
          whatToDo: this.translate.instant("PLEASE_CONTACT_YOUR_ADMINSTRATOR_TO_RESOLVE_THIS_ISSUE")
        })
      }
    })
  }
  optimizeRoute(){
    this.orderStore.dispatch(new orderActions.OptimizeRouteHttp({orderIds:this.selectedOrderIds,offset:this.firstSelectedOrderOffset}))
    this.orderStore.select(orderSelectors.selectLoading).pipe(filter(loading=>!loading),take(1)).subscribe(()=>{
      this.orderStore.dispatch(new orderActions.LoadHttp({ "offset": 0 }))
    })
  }
  displayReports(orderReports : any){
    let inputs = []
        for (let i = 0; i < orderReports.length; i++) {
          let input = {
            type: "radio",
            name: orderReports[i].name,
            value: orderReports[i],
            label: this.translate.instant(orderReports[i].name)
          }
          inputs.push(input);
        }
        const alertInputs: AlertInput[] = inputs as AlertInput[];
          this.alertCtrl.create({
            header: this.translate.instant('PRINT'),
            mode:'ios',
            inputs: alertInputs.map((input: any, index: number) => ({
              ...input,
                checked: index === 0
            })),
            buttons: [
              {
              text: this.translate.instant('CANCEL'),
              role: 'destructive',
              cssClass: 'secondary',
              handler: () => {
                console.log('Confirm Cancel');
              }
            },
            {
              text: this.translate.instant('YES'),
              handler: (report) => {
                this.printService.printReport(report.report_name,'rb_delivery.order',this.selectedOrderIds,report.name)
                this.selectedOrderIds=[]
              }
            }]
          }).then(alert => alert.present());
  }
  selectDates(event: SelectCustomEvent) {
    if (!!event.detail.value) {
      this.dateDomain = [];
    }
    let startDate;
    let endDate;
    let selectedFilterType;
    const value = event.detail.value;
    this.selectedDateFilter = value;
    const handleDateRange = (startFunc:any, endFunc:any, range:string|null) => {
      startDate = this.timeService.dateTimeToOdooFormat(
        this.timeService.getUTCFormat(startFunc(range))
      );
      endDate = this.timeService.dateTimeToOdooFormat(
        this.timeService.getUTCFormat(endFunc(range))
      );
    };
    switch (value) {
      case 'TODAY':
        handleDateRange(this.timeService.getStartDate, this.timeService.getEndDate, null);
        break;
      case 'YESTERDAY':
        handleDateRange(this.timeService.getYesterdayStartDate, this.timeService.getYesterdayEndDate, null);
        break;
      case 'THIS_WEEK':
        handleDateRange(this.timeService.getStartDate, this.timeService.getEndDate, 'week');
        break;
      case 'THIS_MONTH':
        handleDateRange(this.timeService.getStartDate, this.timeService.getEndDate, 'month');
        break;
      case 'THIS_YEAR':
        handleDateRange(this.timeService.getStartDate, this.timeService.getEndDate, 'year');
        break;
      case 'SELECTED_DATE':
        this.modalCtrl.create({
          component: DateSelectorComponent,
          cssClass: 'windowed-modal',
        }).then((modal) => {
          modal.onDidDismiss().then((dateRange) => {
            if (dateRange && dateRange.data) {
              startDate = dateRange.data.start_date;
              endDate = dateRange.data.end_date;
              selectedFilterType = dateRange.data.selected_filter_type;
              startDate = this.timeService.dateTimeToOdooFormat(startDate);
              endDate = this.timeService.dateTimeToOdooFormat(endDate);
              this.filterByDate(startDate, endDate, selectedFilterType);
            } else {
              this.filterByDate();
            }
            this.selectedDateFilter = false;
          });
          modal.present();
        });
        return;
      default:
        this.filterByDate(startDate, endDate);
        break;
    }
    if (value !== 'SELECTED_DATE' && !!value) {
      this.filterByDate(startDate, endDate);
    }
  }
  filterByDate(startDate?:any,endDate?:any,selectedFilterType?:any){
    if(startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);

      if(this.useSlotDateFilter){
        const startDateOnly = start.toISOString().split('T')[0];
        const endDateOnly = end.toISOString().split('T')[0];
        
        this.dateDomain.push(
          '|',
          ['slot_pickup_date', '>=', startDateOnly],
          ['slot_pickup_date', '<=', endDateOnly],
          ['slot_drop_date', '>=', startDateOnly],
          ['slot_drop_date', '<=', endDateOnly]
        );
      } else {
        if (!selectedFilterType) {
          selectedFilterType = 'create_date';
        }
        this.dateDomain.push(
          [selectedFilterType, '>=', start],
          [selectedFilterType, '<=', end]
        );
      }
    }
    this.updateOrdersSearch()
  }
  presentStatusAlert(orderIds: number[], isBulk: boolean = false) {
    // Check if the action sheet is already open, and return if it is
    if (this.isActionSheetOpen) {
        return;
    }
    // Set the flag to indicate that the action sheet is now open
    this.isActionSheetOpen = true;
    this.loadingNextStatuses = true
    this.statusStore.dispatch(new statusActions.LoadNextStatusesHttp([orderIds]))
    combineLatest([
        this.orderStore.select(statusSelector.selectNextStatuses),
        this.statusStore.select(statusSelector.selectNextStatusesLoading)
    ])
    .pipe(filter(([nextStatuses, loading]) => !loading), take(1)).subscribe(([nextStatuses, loading]) => {
        this.nextStatuses = nextStatuses;
        this.loadingNextStatuses = false
        const statusInputs = this.nextStatuses
            .filter((status: any[]) => status[0] !== undefined && status[1] !== undefined)
            .map((status: any[]) => ({
                name: 'status',
                type: 'radio',
                label: status[1],
                value: status[0],
            }));
        if (statusInputs.length < 1) {
            // Display a message indicating there are no valid statuses
            this.alertCtrl.create({
                header: this.translate.instant('WARNING'),
                mode: 'ios',
                message: this.translate.instant('NO_VALID_STATUSES'),
                buttons: [{
                    text: this.translate.instant('OK'),
                    role: 'cancel'
                }]
            }).then(alert => {
                alert.present();
                alert.onDidDismiss().then(()=>{
                  this.isActionSheetOpen = false
                })
            });
        } else {
            this.alertController.create({
                header: this.translate.instant('SELECT_STATUS'),
                mode: 'ios',
                inputs: statusInputs.map((input: any, index: number) => ({
                  ...input,
                  checked: index === 0
              })),
                buttons: [
                    {
                        text: this.translate.instant('CANCEL'),
                        role: 'destructive',
                        cssClass: 'secondary',
                        handler: () => {
                            this.isActionSheetOpen = false;
                        }
                    },
                    {
                        text: this.translate.instant('OK'),
                        handler: async (selectedStatusName) => {
                          let successFollowOrders = await this.followOrdersChecker(orderIds, selectedStatusName)
                          if (!successFollowOrders) {
                            return;
                          }

                          try {
                            if (this.userInfo && this.userInfo[0] && this.userInfo[0].group_id && this.userInfo[0].group_id[0]) {
                              var group_id = this.userInfo[0].group_id[0];
                            }
                            else{
                              var group_id = undefined
                            }
                            var sequence=''
                            var reference_id=''
                            var replacement_order=false
                            let orderTypeId = 0
                            if (orderIds.length === 1) {
                              this.orderStore.select(orderSelectors.selectOrderById(orderIds[0])).pipe(
                                  filter(order => !!order),
                                  take(1)
                              ).subscribe(async order => {
                                  sequence = order.sequence;   
                                  reference_id = order.reference_id
                                  replacement_order = order.replacement_order   
                                  orderTypeId = order.order_type_id[0]
                                                             
                              });
                            }
                            this.alertCtrl.dismiss()
                            let changeOtp = await this.checkIfShouldShowOtp(selectedStatusName,orderIds[0])
                            if (!changeOtp)
                              return

                            let statusActionValues = await this.statusActionService.getStatusActionFields(selectedStatusName,group_id,'olivery_order',false,sequence,reference_id,orderIds[0]);     
                                    
                            if (!(typeof statusActionValues == "string")) {
                                statusActionValues['state'] = selectedStatusName;
                                this.statusStore.select(statusSelector.selectStatusesByName(selectedStatusName,'olivery_order', false)).pipe(take(1))
                                  .subscribe(async status => {
                                    let [shouldScanOnStatus,shouldScanOnOrderType] = await Promise.all([this.validateShouldScanOnStatus(status),this.validateShouldScanOnOrderType(status,orderTypeId)])
                                    
                                    
                                    if (((status.clone || replacement_order ) && shouldScanOnStatus ) || shouldScanOnOrderType) {
                                      let scanResult = await this.openBarcodeScanner()
                                      if(scanResult.data.scanningFinished){
                                        return
                                      }
                                      if(scanResult.data){
                                        statusActionValues['scanned_reference_id'] = scanResult.data
                                      }
                                    }
                                    return this.odooRPC.call("rb_delivery.order", 'execute_status_actions', [statusActionValues, isBulk ? orderIds : [orderIds[0]]]).then((executeStatusActionResponse: any) => {
                                      if (executeStatusActionResponse && executeStatusActionResponse.body && executeStatusActionResponse.body.result && executeStatusActionResponse.body.result.success) {
                                          if (isBulk) {
                                            this.statusChanged = orderIds as []
                                              this.orderStore.dispatch(new orderActions.UpdateBulkHttp({ ids: orderIds }));
                                          } else {
                                            this.statusChanged = [orderIds[0]]
                                              this.orderStore.dispatch(new orderActions.UpdateHttp({ id: orderIds[0] }));
                                          }
                                      } else {
                                          this.dialogService.error({
                                            message: this.translate.instant(executeStatusActionResponse.body.error.data.message),
                                            input: this.translate.instant(executeStatusActionResponse.body.error.data.message),
                                            code: '1203',
                                            whatToDo: this.translate.instant('PLEASE_TRY_AGAIN')
                                          })
                                      }
                                    });
                                  })
                            } else {
                                if (isBulk) {
                                  this.statusChanged = orderIds
                                    this.orderStore.dispatch(new orderActions.UpdateBulkHttp({ ids: orderIds, values: { 'state': selectedStatusName } }));
                                } else {
                                  this.statusStore.select(statusSelector.selectStatusesByName(selectedStatusName,'olivery_order', false)).pipe(take(1))
                                  .subscribe(async status => {
                                    let valuesToChange: { [key: string]: any } = { state: selectedStatusName };
                                    let [shouldScanOnStatus,shouldScanOnOrderType] = await Promise.all([this.validateShouldScanOnStatus(status),this.validateShouldScanOnOrderType(status,orderTypeId)])
                                    

                                    if ((((status && status.clone) || replacement_order ) && shouldScanOnStatus ) || shouldScanOnOrderType) {
                                      let scanResult = await this.openBarcodeScanner()
                                      if(scanResult.data.scanningFinished){
                                        return
                                      }
                                      if(scanResult.data){
                                        valuesToChange['scanned_reference_id'] = scanResult.data
                                      }
                                    }
                                    this.statusChanged = [orderIds[0]]
                                    this.orderStore.dispatch(new orderActions.UpdateHttp({ id: orderIds[0], values : valuesToChange }));
                                  })
                                }
                            }
                        } catch (error:any) {
                          if (typeof error === 'object' && error !== null) {
                            // Handle the case where error is an object
                            const errorMessage = error.message ? this.translate.instant(error.message) : '';
                            const whatToDoMessage = error.whatToDo ? this.translate.instant(error.whatToDo) : '';
                            this.dialogService.error({
                                message: errorMessage,
                                whatToDo: whatToDoMessage,
                                input: errorMessage,
                                code: '1202',
                            });
                        } else {
                            // Fallback for non-object errors
                            if (error != 'PLEASE_CLICK_SUBMIT_TO_CHANGE_THE_STATUS') {
                              this.dialogService.error({
                                  message: this.translate.instant(error),
                                  input: this.translate.instant(error),
                                  code: '1202',
                              });
                            } 
                        }
                        }
                            this.isActionSheetOpen = false;
                        }
                    }
                ]
            }).then(alert => {
                alert.present();
                alert.onDidDismiss().then(()=>{
                  this.isActionSheetOpen = false
                })
            });
        }
    });
    this.statusChanged=[]
}
  async checkIfShouldShowOtp(selectedStatusName: any, order_id: number) {
    let checkIfShouldShowOtp = await this.odooRPC.call('rb_delivery.order', 'check_if_should_show_otp', [order_id, selectedStatusName]);
    if (checkIfShouldShowOtp && checkIfShouldShowOtp.body && checkIfShouldShowOtp.body.result && checkIfShouldShowOtp.body.result.result) {
      const modal = await this.modalCtrl.create({
        component: OtpVerificationComponent,
        cssClass: 'otp-verification-modal',
        mode:'ios',
        componentProps: {
          recordId: order_id
        }
      });
      await modal.present();
      const { data } = await modal.onDidDismiss();
      if (data && data.verified) {
        return true;
      } else {
        this.dialogService.error({
          message: this.translate.instant('OTP_VERIFICATION_FAILED'),
          input: this.translate.instant('OTP_VERIFICATION_FAILED'),
          code: '1300'
        });
        return false
      }
    } else {
      return true;
    }
  }

  async validateShouldScanOnOrderType(status: IStatus, orderTypeId: number): Promise<boolean> {
    let shouldScan = false
    let orderType:any = false
    let orderTypeResult = await this.odooRPC.searchRead('rb_delivery.order_type',[['id','=',orderTypeId]],['clone_status'],1,0,'')
    if (orderTypeResult && orderTypeResult.body && orderTypeResult.body.result && orderTypeResult.body.result.result) {
      orderType = orderTypeResult.body.result.result[0]
      if (orderType && orderType.clone_status && orderType.clone_status.length) {
        let cloneStatus = orderType.clone_status.length > 0 ? orderType.clone_status[0] :false
        shouldScan = cloneStatus == status.id
        return shouldScan
      } else {
        return shouldScan
      }
    }else{
      return shouldScan
    }
    
  }

  async validateShouldScanOnStatus(status: IStatus): Promise<boolean> {
    let shouldScan = false
    let shoulScanConfig = await this.clientConfigurationStore.select(clientConfigurationsSelector.selectClientConfigurationByKeys(['scan_new_reference_id_when_clone','replacement_order_status'])).pipe(filter((keys) => keys && keys.length > 0), take(1)).toPromise()
    let scanWhenClone = false
    let statusIsInReplacementStatuses = false
    for(let config of shoulScanConfig){
      if (config.key == 'scan_new_reference_id_when_clone') {
        scanWhenClone = config.value
      }else if(config.key == 'replacement_order_status'){
        statusIsInReplacementStatuses = config.status.includes(status.id)
      }
    }
    shouldScan = scanWhenClone && statusIsInReplacementStatuses
    return shouldScan
  }
  async getStatusActions(status_action_ids: any[]) {
    let actions = await this.odooRPC.searchRead('rb_delivery.status_action', [['id', 'in', status_action_ids]], ['name'], 0, 0, 'create_date desc')
    let status_actions = []
    if (actions && actions.body && actions.body.result && actions.body.result.result) {
      for (let status of actions.body.result.result) {
        status_actions.push(status.name)
      }
    }
    return status_actions
  }

  async updateAddressNote(orderId: number) {
    const alert = await this.alertController.create({
      mode: 'ios',
      header: this.translate.instant('ADDRESS_NOTE'),
      inputs: [
        { name:'driverNote',
          type: 'text',
          placeholder: this.translate.instant('ADDRESS_NOTE')
        }
      ],
      buttons: [
        {
          text: this.translate.instant('CANCEL'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: this.translate.instant('OK'),
          handler: (data) => {
            this.orderStore.dispatch(new orderActions.UpdateHttp({ id: orderId, values: { 'driver_note': data.driverNote } }));
          }
        }
      ]
    });

    await alert.present();
  }

  async followOrdersChecker(orderIds: any, selectedStatusName: string) {
    let successFollowOrders = true
    if (orderIds.length === 1) {
      let goIntoFollowOrdersScan = await this.followOrdersService.validateIfShouldScanFollowOrders(selectedStatusName)

      if (goIntoFollowOrdersScan) {
        const order = await this.orderStore.select(orderSelectors.selectOrderById(orderIds[0])).pipe(take(1)).toPromise();
        if (order != undefined) {
          const follower_obj = await this.followOrdersService.getFollowSequences(order.sequence);
        
          if (follower_obj && follower_obj.length > 0) {
            const names = follower_obj.map((item: { name: string }) => item.name);
            const barcodes = follower_obj.map((item: { follow_up_sequence: string }) => item.follow_up_sequence);
            await this.alertCtrl.dismiss()
            const modal = await this.modalCtrl.create({
              component: FollowerOrderScannerComponent,
              cssClass: "hide-when-scan-barcode",
              componentProps: {
                change: false,
                originalSequence: order.sequence,
                sequenceBarcodeList: barcodes,
                nameList: names,
              }
            });
            
            await modal.present();
            
            const { data } = await modal.onDidDismiss();
            
            if (data) {
              successFollowOrders = true;
            } else {
              successFollowOrders = false;
            }
          }
        }
      }
    }
    return successFollowOrders
  }

toggleCollapseAllCards(){
  this.orderStore.dispatch(new orderActions.ToggleCollapseAllCards)
}

handleRefresh(event?:RefresherCustomEvent,routeResequence?:any){
  if (routeResequence || this.routeCollectionPage){
    this.updateOrdersSearch(undefined,'route_sequence')
  }
  else{
    this.updateOrdersSearch()
  }
  if(event)
  this.orderStore.select(orderSelectors.selectLoading).pipe(filter(loading=>!loading),take(1)).subscribe(()=>{
    event.target.complete()
  })
}


selectBarcodeAction(){
  let buttons = [
    {
      text:this.translate.instant('SEARCH_ON_ORDER'),
      handler:(()=>{
        this.startScanning('searchOrder')
      }),
      icon:'search'
    },
    {
      text:this.translate.instant('CHANGE_STATUS'),
      handler:(()=>{
        this.startScanning('changeStatus')
      }),
      icon:'create'
    },
    {
      text: this.translate.instant('CANCEL'),
      role: 'cancel',
      icon: 'close'
    },
  ]
    let header = this.translate.instant('BARCODE_ACTIONS')
    let errorMessage = this.translate.instant('NO_BARCODE_ACTIONS_FOUND')
    this.showActionSheet(header,buttons,errorMessage)
  }
  async startScanning(action:string){
    if(action == 'searchOrder'){
      this.openBarcodeScanner().then(scanResult=>{
        if(scanResult.data.scanningFinished){
          return
        }
        if(scanResult.data){
          this.searchControl.setValue(scanResult.data)
        }
      })
    }
    else if (action == 'changeStatus'){
      this.checkStatusHavingAccess()
    }
  }
  openBarcodeScanner(componentProps={}): Promise<any> {
    this.scanActivate = true;
    return new Promise((resolve, reject) => {
      this.modalCtrl.create({
        component: BarcodeScanComponent,
        backdropDismiss:false,
        componentProps,
        cssClass:"exclude-hide"
      }).then(modal => {
        modal.present();
        modal.onDidDismiss().then(scanResult => {
          this.scanActivate = false;
          resolve(scanResult);
        })
      })
    });
  }

  async presentAlert(): Promise<void> {
    const alert = await this.alertController.create({
      header: this.translate.instant('PERMISSION_DENIED'),
      message: this.translate.instant('PLEASE_GRANT_CAMERA_PERMISSION'),
      buttons: [this.translate.instant('OK')]
    });
    await alert.present();
  }
    checkStatusHavingAccess(){
    let buttons = []
    for (let state of this.statusesHavingAccess){
      buttons.push({
        text:state.title,
      handler:(()=>{
        this.openBarcodeListModel(state)
      }),
      })
    }
    buttons.push({
      text: this.translate.instant('CANCEL'),
      role: 'cancel',
      icon: 'close'
    })
    let header = this.translate.instant('STATUS')
    let error = this.translate.instant('THER_IS_NO_STATUSES')
    this.showActionSheet(header,buttons,error)
  }
  async openBarcodeListModel(state : any){
    let goIntoFollowOrdersScan = await this.followOrdersService.validateIfShouldScanFollowOrders(state.name)
    this.openBarcodeScanner({typeSelectionItems:['SEQUENCE','REFERENCE']}).then(scanResult=>{
      if(scanResult.data.scanningFinished){
        return
      }
      if (scanResult.data) {
        (async () => {
          console.log(scanResult)
          let type_ref = false
          if (scanResult.data.type == 'REFERENCE')
            type_ref = true
          var follower_obj = await this.followOrdersService.getFollowSequences(scanResult.data.scannedValue);
          if(follower_obj && follower_obj.length > 0 && goIntoFollowOrdersScan){
            const names = follower_obj.map((item: { name: string }) => item.name);
            const barcodes = follower_obj.map((item: { follow_up_sequence: string }) => item.follow_up_sequence);
            this.modalCtrl.create({
              component : FollowerOrderScannerComponent,
              cssClass:"hide-when-scan-barcode",
              componentProps :{
                state : state,
                originalSequence : scanResult.data.scannedValue,
                sequenceBarcodeList : barcodes,
                nameList: names,
                is_reference: type_ref,
                change:false
              }}).then(modal =>{
                modal.present()

                modal.onDidDismiss().then(()=>{
                  this.modalCtrl.create({
                    component : UpdateMultiOrdersComponent,
                    cssClass:"hide-when-scan-barcode",
                    componentProps :{
                      state : state,
                      barcodeList : [scanResult.data.scannedValue],
                      firstScanType : scanResult.data.type
                    }}).then(modal =>{
                      modal.present()
                      modal.onDidDismiss().then(()=>{
                        this.handleRefresh()
                      })
                    })
                })
              })
          } else {
            this.modalCtrl.create({
              component : UpdateMultiOrdersComponent,
              cssClass:"hide-when-scan-barcode",
              componentProps :{
                state : state,
                barcodeList : [scanResult.data.scannedValue],
                firstScanType : scanResult.data.type
              }}).then(modal =>{
                modal.present()
                modal.onDidDismiss().then(()=>{
                  this.handleRefresh()
                })
              })
          }
        })();
        }
    })
  }
  
  async openSortPopup(){
    const popover = await this.alertController.create({
      header: this.translate.instant('SORT_ACCORDING_TO:'),
      buttons:[
        {
          text: this.translate.instant('SEQUENCE'), 
          handler: (()=> {
            this.updateOrdersSearch(undefined, 'route_sequence ASC')
          })
        },
        {text:this.translate.instant('CREATE_DATE'),handler:(()=>{
            this.updateOrdersSearch(undefined,'create_date DESC')
        })}
      ]
      
    });
    await popover.present();
  }

  chatOrder(orderId:number){
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      const modal = this.modalCtrl.create({
        component: OrderChatComponent,
        componentProps: {
          chatId:order.chat_id,
          order_id:order.id,
          order_sequence:order.sequence,
          current_user_id:this.userInfo[0].user_id[0],
          second_tittle:order.customer_name+'-'+order.assign_to_business[1]
        }
      });
      modal.then((mdl) => mdl.present())
    })
  }

  updateReference(orderId:number){
    this.openBarcodeScanner().then(scanResult=>{
      if(scanResult.data && typeof scanResult.data == 'string'){
        this.alertController.create({
          header:this.translate.instant("REFERENCE_ID_WILL_BE_UPDATED_TO"),
          message:scanResult.data,
          mode:'ios',
          buttons:[
            {
              text:this.translate.instant("CONFIRM"),
              handler:(()=>{
                this.orderStore.dispatch(new orderActions.UpdateHttp({ id: orderId, values : {'reference_id': scanResult.data} }))
              })
            },
            {
              text:this.translate.instant("CANCEL"),
              role:"cancel"
            }
          ]
          
        }).then(alert=>alert.present())
      }
    })
  }

  clientOrderHistory(orderId:number){
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe(order=>{
      if(order && order.customer_mobile){
        this.modalCtrl.create({
          component: OrdersPage,
          initialBreakpoint: 0.65,
          breakpoints: [0, 0.25, 0.5, 0.75, 1],
          handleBehavior: 'cycle',
          mode: 'ios',
          componentProps: {
            domain: [['customer_mobile', '=', order.customer_mobile]],
            buttonsToHide: ['clientOrderHistory'],
            loadIntoTempOrders: true
          }
        }).then(modal => {
          modal.present()
        })
      }
    })
  }
  loadPaymentTypes(){
    this.odooRPC.searchRead(
      'rb_delivery.payment_type',
      [['is_online','=',true]],
      [
        'id',
        'name',
        'default',
        'is_online',
        'url_field_name',
        'create_billing_api_name',
        'check_billing_api_name'
      ],
      0,
      0,
      'create_date DESC'
    ).then(response=>{
      
      this.paymentTypes = response.body.result.result
    })
  }
  showPayment(orderId: number) {
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe((order:any) => {
      if (order) {
        let buttons = []
        for (let paymentType of this.paymentTypes) {
            buttons.push({
                text: this.translate.instant(paymentType.name),
                handler: () => {
                  this.openPayment(paymentType, order)
                }
              }
            )
          
        }
        buttons.push({
          text: this.translate.instant('CANCEL'),
          role: 'cancel'
        })
        try {
  
          const actionSheet = this.actionSheetCtrl.create({
            mode: 'ios',
            buttons: buttons,
  
  
          });
          actionSheet.then(as => as.present());
  
        }
        catch (err) { console.log(err) }
  
      }
    })
  
  }
  openPayment(paymentType:any,order:IOrder){
        
    let keys = Object.keys(order)
    let urlFieldIndex:number | undefined = undefined
    for(let i=0;i<keys.length;i++){
      if(keys[i]==paymentType.url_field_name){
        urlFieldIndex=i
      }
    }
    if(!urlFieldIndex){
      return
    }
    let url = Object.values(order)[urlFieldIndex]
    let browser
    if(!!url){
      browser = this.iab.create(url,'_blank','location=no,hardwareback=no')
      this.preparePaymentExit(browser,paymentType,order.id)
    }
  
    else this.odooRPC.call('rb_delivery.order', paymentType.create_billing_api_name, [
      order.id,
      true
    ]).then(async data=>{
      if(data.body.result.success && data.body.result.result){
        
        let tempUrl=data.body.result.result
        browser = this.iab.create(tempUrl,'_blank','location=no,hardwareback=no')
        this.preparePaymentExit(browser,paymentType,order.id)
        this.handleRefresh()
      }
      else {
        const toast = await this.toastCtrl.create({
          message: this.translate.instant('THERE_IS_NO_PAYMENT_REQUIRED'),
          duration: 3000,
          position: 'top'
        });
        toast.present();
      }
    })
  }

  openLocationsAlert(orderId:number){
    this.orderStore.select(orderSelectors.selectOrderById(orderId)).pipe(take(1)).subscribe((order:any) => {
      if (order) {
        let locationButtons:ActionSheetButton[]=[]
        if ((order?.business_alt_longitude && order?.business_alt_latitude) || order?.business_alt_postal_code){
          locationButtons.push({
            text:this.translate.instant('BUSINESS_LOCATION'),
            handler:()=>{this.openLocationAppSelector(order.business_alt_latitude,order.business_alt_longitude, order.business_alt_postal_code)}
          })
        }
        else if ((order.business_longitude && order.business_latitude) || order?.business_postal_code){
          locationButtons.push({
            text:this.translate.instant('BUSINESS_LOCATION'),
            handler:()=>{this.openLocationAppSelector(order.business_latitude,order.business_longitude, order.business_postal_code)}
          })
        }
        if ((order.longitude && order.latitude) || order?.customer_postal_code){
          
          locationButtons.push({
            text:this.translate.instant('CUSTOMER_LOCATION'),
            handler:()=>{this.openLocationAppSelector(order.latitude,order.longitude, order.customer_postal_code)}
          })
        }

        if(locationButtons.length>0){
          this.actionSheetCtrl.create({
            mode:'ios',
            buttons:locationButtons
          }).then(actSheet=>actSheet.present())
        }
        else{
          this.toastCtrl.create({
            message: this.translate.instant('THERE_IS_NO_LOCATIONS_PROVIDED'),
            duration: 3000,
            position: 'top'
          }).then(toast=>toast.present())
        }
  
      }
    })
  }

  openLocationAppSelector(latitude?: string, longitude?: string, postalCode?: string) {
    let locationAppsButtons: ActionSheetButton[] = [];

    const hasCoordinates = latitude && longitude;
    const hasPostalCode = postalCode;
    if (hasCoordinates) {
      locationAppsButtons = [
        {
          icon: IconsMap.googleMaps,
          text: this.translate.instant('GOOGLE_MAPS'),
          handler: () => { window.open('https://www.google.com/maps?q=' + latitude + ',' + longitude) }
        },
        {
          icon: IconsMap.waze,
          text: this.translate.instant('WAZE'),
          handler: () => { window.open('https://waze.com/ul?ll=' + latitude + ',' + longitude + '&navigate=yes') }
        },
        {
          icon: "location-outline",
          text: this.translate.instant("OPEN_IN_ANOTHER_APP"),
          handler: () => { window.open('geo:' + latitude + ',' + longitude) }
        }
      ];
    } else if (hasPostalCode) {
      locationAppsButtons = [
        {
          icon: IconsMap.googleMaps,
          text: this.translate.instant('GOOGLE_MAPS'),
          handler: () => { window.open('https://www.google.com/maps?q=' + encodeURIComponent(postalCode)) }
        }
      ];
    } else {
      console.error('No location data provided (coordinates or postal code)');
      return;
    }

    this.actionSheetCtrl.create({
      mode: 'ios',
      buttons: locationAppsButtons
    }).then(actSheet => actSheet.present());
  }
  
  closeModal(){
    this.modalCtrl.dismiss()
  }

  preparePaymentExit(browser:InAppBrowserObject,paymentType:any,orderId:number){
    setTimeout(()=>{
      
    browser.on('loadstart').subscribe(async event=>{
      
      if(event.url.includes("olivery")){
        if(event.url.includes("status=paid")){
          const loading = await this.loadingCtrl.create({
          message: this.translate.instant("PLEASE_WAIT"),
          duration: 4000
          });
          await loading.present();
          
          this.odooRPC.call('rb_delivery.order', paymentType.check_billing_api_name, [
            orderId,
            true
          ]).then(async data=>{
            
              if(!!data && data=="paid"){
                const toast = await this.toastCtrl.create({
                  message: this.translate.instant('PAYMENT_SUCCESS'),
                  duration: 3000,
                  position: 'top'
                });
                toast.present();
                this.handleRefresh()
                await loading.dismiss()
              }
            })
          
        }
        browser.close()
      }
    })
    },500)
  }
  openRoutesList(){
    
    this.odooRPC.searchRead('rb_delivery.route_collection',[['order_ids','!=',false],['state','!=','done_route'], ['assign_to_agent', '=', this.userInfo[0].id]],['name', 'order_ids', 'route'],0,0,'').then(response=>{
      let routes:any=false
      if(response && response.body && response.body.result && response.body.result.result){
        routes=response.body.result.result
        let buttons:ActionSheetButton[]=[]
        for (let route of routes){
          let routeid =  route.route && route.route.length>0 ? route.route[0] : false
          buttons.push({
            text:route.name,
            handler:()=>{
              this.openRouteCollectionPage(route.order_ids, route.id, routeid)
            }
          })
        }
        buttons.push({
          text:this.translate.instant('CREATE_OR_UPDATE_ROUTE'),
          handler:()=>{
            this.openCreateRouteDialog()
          }
        })
          
        
        this.actionSheetCtrl.create({
          buttons,
          header:this.translate.instant("CHOOSE_A_ROUTE")
        }).then(actionSheet=>actionSheet.present())
      }
      else{
        this.openRouteMap()
      }
    })
  }
  async openCreateRouteDialog() {
    const modal = await this.modalCtrl.create({
      component: CreateRouteCollectionComponent,
      cssClass: 'sort-and-destribute-dialog',
    });
  
    modal.onDidDismiss().then(async (result) => {
      if (result.data?.date) {
        const loadingToast = await this.toastCtrl.create({
          message: this.translate.instant('CREATING_ROUTE_COLLECTION'),
          duration: 0,
          position: 'bottom',
        });
        await loadingToast.present();
  
        try {
          let res = await this.odooRPC.createRecord('rb_delivery.route_collection', {
            'order_ids': this.selectedOrderIds,
            'assign_to_agent': this.userInfo[0].id,
            'route_date': result.data?.date
          });
  
          await loadingToast.dismiss();
  
          if (res && res.body && res.body.error && res.body.error.data && res.body.error.data.message) {
            this.dialogService.error({
              input:res.body.error.data.message,
              message:res.body.error.data.message
            })
            return;
          }
  
          // Show success toast
          const successToast = await this.toastCtrl.create({
            message: this.translate.instant('ROUTE_CREATED_SUCCESSFULY'),
            duration: 3000,
            position: 'bottom',
            color: 'success',
          });
          await successToast.present();
  
          let route = res.body.result.result[0];
          this.openRouteCollectionPage(route.order_ids, route.id, route.route[0]);
        } catch (error) {
          await loadingToast.dismiss();
          const errorToast = await this.toastCtrl.create({
            message: this.translate.instant('AN_ERROR_OCCURED_WHILE_CREATING_ROUTE'),
            duration: 3000,
            position: 'bottom',
            color: 'danger',
          });
          await errorToast.present();
        }
      } else {
        console.log('Dialog cancelled.');
      }
    });
  
    await modal.present();
  }

  openRouteCollectionPage(order_ids?: any, routeCollectionId?:any, routeId?: any) {
    this.modalCtrl.create({
      component:RouteCollectionComponent,
      backdropDismiss:false,
      componentProps:{ 
        order_ids: order_ids,
        routeCollectionId: routeCollectionId,
        routeId: routeId

      }
    }).then(modal=>{
      modal.present()
    })
  }

  openRouteMap(route=false,domain=[['state','=','in_progress']]){
    this.odooRPC.searchRead('rb_delivery.order',domain,orderFieldsForRouteOptimization,0,0,'').then(response=>{
      if(response && response.body && response.body.result && response.body.result.result && response.body.result.result.length>0){
        this.modalCtrl.create({
          component:LocationSelectorComponent,
          backdropDismiss:false,
          componentProps:{
            route,
            orders:response.body.result.result,
            userInfo:this.userInfo,
            showSubmit: false
          }
        }).then(modal=>{
          modal.present()
        })
      }else{
        this.toastCtrl.create({
          message: this.translate.instant('NO_ORDERS_WITH_IN_PROGRESS_STATUS'),
          duration: 3000,
          position: 'top'
        }).then(toast=>{
          toast.present()
        });
      }
    })
  }

  locationMap(order:any) {
    this.orderStore.select(orderSelectors.selectOrderById(order.id)).pipe(take(1)).subscribe(order=>{
      let searchString = ''
      let latitude = ''
      let longitude = ''
      if (order.customer_area){
        if (typeof order.customer_area == 'object') {
          searchString+= order.customer_area[1]
        }
      }

      if (order.customer_sub_area) {
        if (searchString)
          searchString +=', '

        if (typeof order.customer_sub_area == 'object') {
          searchString+= order.customer_sub_area[1]
        }
      }

      if (order.customer_address){
        if (searchString)
          searchString +=', '

        searchString += order.customer_address
      }

      if (order.latitude) {
        latitude = order.latitude
      }
      if (order.longitude) {
        longitude = order.longitude
      }

      this.modalCtrl.create({
        component:LocationSelectorComponent,
        componentProps: {
          searchString: searchString,
          longitude: longitude,
          latitude: latitude
        },
        backdropDismiss:false,
      }).then(modal=>{
        modal.present()
        modal.onDidDismiss().then(locationResult=>{
          
          if(locationResult && locationResult.data){
            if (locationResult.data && locationResult.data.longitude && locationResult.data.latitude) {
              this.orderStore.dispatch(new orderActions.UpdateHttp({ id: order.id, values : {'longitude': locationResult.data.longitude, 'latitude': locationResult.data.latitude} }));
            }
          }
        })
      })
    })
  }
  async openOrderSlotSelector() {
    if (this.orderSlots.length === 0) {
      const noSlotsAlert = await this.alertController.create({
        header: this.translate.instant('NO_SLOTS'),
        message: this.translate.instant('NO_SLOTS_AVAILABLE'),
        buttons: [this.translate.instant('OK')],
        mode: 'ios',
        animated: true
      });
      await noSlotsAlert.present();
      return;
    }
  
    const alert = await this.alertController.create({
      header: this.translate.instant('SELECT_SLOT'),
      inputs: this.orderSlots.map((slot: string) => {
        return {
          name: slot,
          type: 'radio',
          label: slot,
          value: slot,
          checked: false
        };
      }),
      buttons: [
        {
          text: this.translate.instant('CANCEL'),
          role: 'cancel',
        },
        {
          text: this.translate.instant('OK'),
          handler: (selectedSlot: string) => {
            this.searchControl.setValue(selectedSlot);
          },
        },
      ],
      mode: 'ios',
      animated: true
    });
    await alert.present();
  }
  
  

  proceedOpenRouteCollectionIdMap() {
    if (this.routeId) {
      this.odooRPC.searchRead(
        'rb_delivery.routes',
        [['id', '=', this.routeId]],
        ['locations', 'direction', 'order_ids', 'create_date', 'done_locations', 'name', 'route_collection_id'],
        0,
        0,
        ''
      ).then(async response => {
        let routes: any = false;
        if (
          response &&
          response.body &&
          response.body.result &&
          response.body.result.result &&
          response.body.result.result.length > 0
        ) {
          routes = response.body.result.result;
          this.openRouteMap(routes[0], [['id', 'in', routes[0].order_ids]]);
        }
      });
    } else {
      this.locationService.locationSetup({ required: false }).then(async data => {
        let newRoute = await this.odooRPC.call(
          'rb_delivery.route_collection',
          'create_route',
          [this.routeCollectionId, data.longitude, data.latitude]
        );
        if (
          newRoute &&
          newRoute.body &&
          newRoute.body.result &&
          newRoute.body.result.result
        ) {
          let route = newRoute.body.result.result[0];
          this.openRouteMap(route, [['id', 'in', route.order_ids]]);
        }
      });
    }
  }

    async addFollowOrder(orderId: number) {
  
      let seqArray: string[] = [];
      let nameArray: string[] = [];
      let editable: boolean[] = [];
      let showAddFollowOrder: boolean = true;
  
      try {
          var search_domain = [['order_id', '=', orderId]];
          const data = await this.odooRPC.call("rb_delivery.follow_up_order", 'search_read', [search_domain, ['follow_up_sequence', 'name']]);
          if (data.body.result.success == true && data.body.result.result.length > 0) {
            data.body.result.result.forEach((item: {follow_up_sequence: string; name: string;}) => {
              seqArray.push(item.follow_up_sequence);
              nameArray.push(item.name);
              editable.push(true);
            });
          }
        
        // we need to get the real data for current order
        var old_seqArray = [...seqArray];
          interface JsonItem {
            sequence: string;
            name: string;
          }
          const modal = await this.modalCtrl.create({
              component: FollowOrderCreatorComponent,
              cssClass: 'exclude-hide',
              componentProps: {
                  followOrdersSequences: seqArray,
                  followOrdersNames: nameArray,
                  editable: editable,
                  showAddFollowOrder: showAddFollowOrder,
              },
          });
  
          modal.present();
          const createRes = await modal.onDidDismiss();
  
  
          if (createRes.data) {
            let ordersUpdate:any = []
            const jsonArray: { sequence: string; name: string }[] = [];
            for (let i = 0; i < Math.min(createRes.data.followOrdersSequences.length, createRes.data.followOrdersNames.length); i++) {
              if (old_seqArray.includes(createRes.data.followOrdersSequences[i])) {
                continue;
              }
              ordersUpdate.push([0,0,{
                'order_id': orderId,
                'name': createRes.data.followOrdersNames[i],
                'follow_up_sequence': createRes.data.followOrdersSequences[i]
              }])
            }
            this.orderStore.dispatch(new orderActions.UpdateHttp({ id: orderId, values: {'follow_up_order': ordersUpdate}}));
  
            this.dialogService.success({
              input: this.translate.instant('FOLLOW_UP_ORDER_CREATED_SUCCESSFULLY'),
              message: this.translate.instant('FOLLOW_UP_ORDER_CREATED_SUCCESSFULLY'),
          });
    
          }
      } catch (error) {
          console.error('Error fetching follow up orders:', error);
      }
  }

  async addNote(orderId: number) {
    const alert = await this.alertCtrl.create({
      mode: 'ios',
      header: this.translate.instant('NOTE'),
      inputs: [
        { name:'note',
          type: 'text',
          placeholder: this.translate.instant('NOTE')
        }
      ],
      buttons: [
        {
          text: this.translate.instant('CANCEL'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: this.translate.instant('OK'),
          handler: (data) => {
            let roleCode = this.userInfo[0].role_code
            switch(roleCode) {
              case 'rb_delivery.role_driver':
                this.orderStore.dispatch(new orderActions.UpdateHttp({ id: orderId, values: { 'driver_note': data.note } }));
                break;
              case 'rb_delivery.role_business':
                this.orderStore.dispatch(new orderActions.UpdateHttp({ id: orderId, values: { 'product_note': data.note } }));
                break;
              default:
              this.orderStore.dispatch(new orderActions.UpdateHttp({ id: orderId, values: { 'note': data.note } }));
            }
          }
        }
      ]
    });
    await alert.present();
  }


  async openFollowUpOrders(orderId: number) {
    let seqArray:any = [];
    let nameArray:any = [];
    let editable:any = [];
    var search_domain = [['order_id', '=', orderId]];
    const data = await this.odooRPC.call("rb_delivery.follow_up_order", 'search_read', [search_domain, ['follow_up_sequence', 'name']]);
    if (data.body.result.success == true && data.body.result.result.length > 0) {
      data.body.result.result.forEach((item: {follow_up_sequence: string; name: string;}) => {
        seqArray.push(item.follow_up_sequence);
        nameArray.push(item.name);
        editable.push(false);
      });
    }
    const modal = await this.modalCtrl.create({
        component: FollowOrderCreatorComponent,
        cssClass: 'exclude-hide',
        componentProps: {
            editable: editable,
            followOrdersSequences: seqArray,
            followOrdersNames: nameArray,
            showAddFollowOrder: false,
            disableEdit: true,
        },
    });

    modal.present();
  }


  recalculateRoute() {
    this.triggerParentFunction();
  }
}
enum OperationTypes {
  callCustomer = 'callCustomer',
  printOrder = 'printOrder',
  editOrder = 'editOrder',
  showAttachment = 'showAttachment',
  chatOrder = 'chatOrder',
  showOrderHistory = 'showOrderHistory',
  updateReference = 'updateReference',
  clientOrderHistory = 'clientOrderHistory',
  showPayment = 'showPayment',
  addNote = 'addNote',
  addFollowOrder = 'addFollowOrder',
  openFollowUpOrders = 'openFollowUpOrders',
}

