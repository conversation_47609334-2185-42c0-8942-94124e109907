
export interface IOrder {
  business_postal_code: any;
  customer_postal_code: any;
  ids : any[]
  web_color: string;
  type:'order';
  business_alt_sub_area: boolean;
  show_alt_address: IOrder;
  billing_type: any;
  id: number;
  order_type_id: any[];
  customer_name: string;
  customer_mobile: string;
  second_mobile_number: string;
  cus_whatsapp_mobile: string;
  cus_second_whatsapp_mobile: string;
  customer_area: number;
  customer_sub_area:number;
  customer_address: string;
  sequence: string;
  reference_id: string;
  create_date: string;
  sender_mobile: string;
  financial_state: string;
  business_mobile_number: string;
  business_area: number;
  business_address: string;
  business_location: string;
  business_name: string;
  assign_to_business: any[];
  assign_to_agent: any[];
  current_branch: string;
  to_branch: string;
  delivery_cost_on_sender: boolean;
  cost: number;
  copy_total_cost: number;
  total_cost: number;
  delivery_cost: number;
  money_collection_cost: number;
  required_from_business: number;
  note: string;
  stuck_comment: string;
  state: string;
  reject_reason: any[];
  product_note: string;
  description_tags: any[];
  latitude: string;
  longitude: string;
  follower_store_name: string;
  write_date: string;
  second_business_mobile_number: string;
  inclusive_delivery: boolean;
  follower_address:string;
  follower_area:string; 
  follower_mobile_number:string; 
  follower_second_mobile_number:string; 
  follower_ref_id:string;
  show_follower_info:boolean; 
  picking_from_time:string; 
  picking_to_time:string;
  replacement_order:boolean;
  clone_reference:any[];
  replacement_reference:any[];
  customer_village:string;
  customer_neighbour:string;
  customer_building_number:string;
  no_of_items:any;
  commercial_number:string;
  is_replacement:boolean;
  show_picking_time:boolean;
  location_url:string;
  business_whatsapp_mobile:string;
  business_second_whatsapp_mobile:string;
  route_id:any[];
  agent_commercial_number:string;
  delivery_profit:number;
  agent_cost:number;
  route_date:string;
  service: any[];
  business_longitude: string;
  business_latitude: string;
  commercial_name:string;
  chat_id:any[];
  reschedule_date:Date;
  business_sub_area:string;
  is_cloner:boolean;
  is_cloned:boolean;
  is_returned:boolean;
  cloned_order_ids:any[];
  returned_clone_reference:any[];
  cloner_order_id:any[];
  customer_location:string;
  dimentional_weight:any[];
  customer_country:any[];
  total_final_weight:number;
  pickup_request_id:any[];
  is_international_order:boolean;
  no_of_orders:number;
  order_weight:string;
  weight_cost:number;
  delivery_from_time:string;
  delivery_to_time:string;
  show_delivery_time:boolean;
  default_vat:number;
  vat_value:number;
  delivery_fee_without_vat:number;
  delivery_company:any[];
  waiting_time:string;
  active_notification_data:any;
  notification_timer:any;
  business_alt_area:any[];
  business_alt_address:string;
  alt_business_name:string;
  alt_mobile_number:string;
  agent_mobile_number:string;
  agent_resident_id:string;
  agent_expiry_date:string;
  agent_nationality:string;
  vehicle_id:any[];
  agent_name:string;
  full_plate_number:string;
  next_status_ids:any[]
  route_sequence: any;
  customer_payment:string;
  follow_up_orders_map:string;
  state_id :any[]
  status_color: string;
  second_agent_mobile_number: string;
  agent_whatsapp_mobile: string;
  agent_second_whatsapp_mobile: string;
  attachment_ids:number[];
  slot_pickup_date:string;
  slot_drop_date:string;
  delivery_slot:string;
  pick_up_slot:string;
  customer_district_id:any[];
  address_tag:any[];
}

export const orderDefaults: Pick<IOrder, 'type'> = {
  type: 'order',
};

export const orderFields = [
  'web_color',
  'ids',
  'id',
  'order_type_id',
  'customer_name',
  'customer_mobile',
  'sender_mobile',
  'second_mobile_number',
  'cus_whatsapp_mobile',
  'cus_second_whatsapp_mobile',
  'customer_area',
  'customer_address',
  'sequence',
  'reference_id',
  'create_date',
  'branch_id',
  'financial_state',
  'business_mobile_number',
  'assign_to_business',
  'assign_to_agent',
  'current_branch',
  'to_branch',
  'delivery_cost_on_sender',
  'cost',
  'total_cost',
  'delivery_cost',
  'copy_total_cost',
  'money_collection_cost',
  'required_from_business',
  'note',
  'stuck_comment',
  'reject_reason',
  'state',
  'description_tags',
  'product_note',
  'business_area',
  'business_address',
  'business_location',
  'latitude',
  'longitude',
  'follower_store_name',
  'business_name',
  'write_date',
  'second_business_mobile_number',
  'inclusive_delivery',
  'follower_address',
  'follower_area',
  'follower_mobile_number',
  'follower_second_mobile_number',
  'follower_ref_id',
  'show_follower_info',
  'picking_from_time',
  'picking_to_time',
  'replacement_order',
  'clone_reference',
  'replacement_reference',
  'customer_village',
  'customer_neighbour',
  'customer_building_number',
  'no_of_items',
  'commercial_number',
  'is_replacement',
  'customer_sub_area',
  'show_picking_time',
  'location_url',
  'business_whatsapp_mobile',
  'business_second_whatsapp_mobile',
  'route_id',
  'agent_commercial_number',
  'delivery_profit',
  'agent_cost',
  'route_date',
  'service',
  'business_longitude',
  'business_latitude',
  'commercial_name',
  'chat_id',
  'reschedule_date',
  'business_sub_area',
  'is_cloner',
  'is_cloned',
  'is_returned',
  'cloned_order_ids',
  'returned_clone_reference',
  'cloner_order_id',
  'customer_location',
  'dimentional_weight',
  'customer_country',
  'total_final_weight',
  'pickup_request_id',
  "is_international_order",
  "no_of_orders",
  "order_weight",
  "weight_cost",
  "delivery_from_time",
  "delivery_to_time",
  "show_delivery_time",
  "default_vat",
  "vat_value",
  "delivery_fee_without_vat",
  "delivery_company",
  "waiting_time",
  "active_notification_data",
  "notification_timer",
  "business_alt_area",
  "business_alt_address",
  "alt_business_name",
  "alt_mobile_number",
  "agent_mobile_number",
  "agent_resident_id",
  "agent_expiry_date",
  "agent_nationality",
  "vehicle_id",
  "agent_name",
  "full_plate_number",
  "business_alt_sub_area",
  "show_alt_address",
  "billing_type",
  "next_status_ids",
  "route_sequence",
  "billing_type",
  'customer_payment',
  'state_id',
  'follow_up_orders_map',
  'status_color',
  'second_agent_mobile_number',
  'agent_whatsapp_mobile',
  'agent_second_whatsapp_mobile',
  'slot_pickup_date',
  'slot_drop_date',
  'delivery_slot',
  'pick_up_slot',
  'customer_district_id',
  'address_tag'
];

export const orderFieldsForRouteOptimization = [
  'id',
  'customer_name',
  'customer_mobile',
  'second_mobile_number',
  'cus_whatsapp_mobile',
  'cus_second_whatsapp_mobile',
  'customer_area',
  'customer_address',
  'sequence',
  'reference_id',
  'latitude',
  'longitude',
  'state_id'
];
