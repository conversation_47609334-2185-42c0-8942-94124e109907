import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActionSheetButton, ActionSheetController, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { IconsMap } from 'src/app/assets-maps/icons-map';
import { RecordCardStructure } from 'src/app/services/record-structure-service';
import { Store } from '@ngrx/store';
import { OrderState } from 'src/app/ngrx-store/order/store';
import * as orderSelectors from 'src/app/ngrx-store/order/store/selectors';
import { filter, take } from 'rxjs';

@Component({
  selector: 'app-record-item-content',
  templateUrl: './record-item-content.component.html',
  styleUrls: ['./record-item-content.component.scss'],
})
export class RecordItemContentComponent implements OnInit {

  @Input() contentInputs!: any
  @Input() contentSideInputs!: any
  @Input() notesInputs!: any
  @Input() theme!:string
  @Output() buttonPress = new EventEmitter<any>()
  expandedItems: any[]=[];
  @Input() hasSideInputs: boolean=true;
  @Input() recordCardStructure !: RecordCardStructure

  @Input() currencySymbol!:string

  @Input() hideButtons: boolean = false

  @Input() isCollapsed: boolean = false

  @Input() showInOneLine: boolean = false
  @Input() tmpSequence: string = ''
  @Input() digits: string="1.2-2"

  constructor(
    private translateService: TranslateService,
    private actionSheetCtrl:ActionSheetController,
    private toastCtrl: ToastController,
    private orderStore: Store<OrderState>,
  ) {
  }

  ngOnInit() {
        console.log(this.notesInputs);
        
        this.fixDates()
        this.filterNotesInputs()
  }
  filterNotesInputs() {
    if (!this.notesInputs) {
      return;
    }
    this.notesInputs = this.notesInputs.filter((element: { name: any; }[]) => {
      if (element[1].name && element[1].name != 'UNDEFINED') {
        if (Array.isArray(element[1].name) && element[1].name.length > 0 && (!this.shouldHideItem(element[1]))) {
          return true;
        } else if (!Array.isArray(element[1].name) && (!this.shouldHideItem(element[1]))) {
          return true;
        }
      }
      return false; 
    });
  }

  fixDates(): void {
    if (!this.contentInputs || !Array.isArray(this.contentInputs)) {
      return;
    }
  
    this.contentInputs.forEach((item, index) => {
      const [fieldKey, fieldData] = item;
  
      if (fieldData && fieldData.field_type === 'datetime' && fieldData.name) {
        const [datePart, timePart] = fieldData.name.split(' ');
        const utcString = `${datePart}T${timePart}Z`;

        const parsedDate = new Date(utcString);

        if (isNaN(parsedDate.getTime())) {
          console.warn(`Invalid date found at index ${index}: ${fieldData.name}`);
          return;
        }

        fieldData.name = parsedDate.toLocaleString();
      }
    });
  }
  
  

  checkInIconMap(iconName: keyof typeof IconsMap) {
    if (iconName in IconsMap) {
      return {
        exist: true,
        path: IconsMap[iconName as keyof typeof IconsMap],
      };
    } else {
      return {
        exist: false,
      };
    }
  }

  checkIfFooterInputs() {
    let hasNamedElement = false;
    if (!this.notesInputs) {
      return hasNamedElement
    }
    this.notesInputs = this.notesInputs.filter((element: { name: any; }[]) => {
      if (element[1].name && element[1].name != 'UNDEFINED') {
        if (Array.isArray(element[1].name) && element[1].name.length > 0 && (!this.shouldHideItem(element[1]))) {
          hasNamedElement = true;
        } else if (!Array.isArray(element[1].name) && (!this.shouldHideItem(element[1]))) {
          hasNamedElement = true;
        }
        return true;
      }
      return false; 
    });
    this.cleanEmptyInputs(); 
    return hasNamedElement;
  }
  cleanEmptyInputs() {
    this.notesInputs = this.notesInputs.filter((element: any) => {
      return element[1].name && element[1].name != 'UNDEFINED';
    });
  }

  emitButtonPress(functionName: string, actionItem?: any) {
    if (functionName !== 'geoLocation' || !actionItem) {
      this.buttonPress.emit(functionName);
      return;
    }

    const isBusinessLocation = actionItem.field_1_name?.includes('business');
    
    this.orderStore.select(orderSelectors.selectOrderById(this.recordCardStructure.id))
      .pipe(filter(order => !!order), take(1))
      .subscribe((order) => {
        const hasLocation = this.checkLocationAvailability(order, isBusinessLocation);
        
        if (!hasLocation) {
          this.showLocationUnavailableToast();
          return;
        }
        
        this.openLocationAppsActionSheet(actionItem, order, isBusinessLocation);
      });
  }

  private checkLocationAvailability(order: any, isBusinessLocation: boolean): boolean {
    if (isBusinessLocation) {
      return order.business_postal_code || (order.business_longitude && order.business_latitude);
    } else {
      return order.customer_postal_code || (order.longitude && order.latitude);
    }
  }

  private showLocationUnavailableToast(): void {
    this.toastCtrl.create({
      header: this.translateService.instant('LOCATION_UNAVAILABLE_PLEASE_MAKE_SURE_TO_PROVIDE_COORDINATES_OR_POSTAL_CODE'),
      duration: 5000,
      position: 'top',
      mode: 'ios',
    }).then(toast => toast.present());
  }

  private openLocationAppsActionSheet(actionItem: any, order: any, isBusinessLocation: boolean): void {
    let locationAppsButtons: ActionSheetButton[] = [];

    const postalCode = isBusinessLocation ? order.business_postal_code : order.customer_postal_code;
    const latitude = isBusinessLocation ? order.business_latitude : order.latitude;
    const longitude = isBusinessLocation ? order.business_longitude : order.longitude;
    
    const hasCoordinates = latitude && longitude;
    const hasPostalCode = postalCode;

    if (hasCoordinates) {
      locationAppsButtons = [
        {
          icon: IconsMap.googleMaps,
          text: this.translateService.instant('GOOGLE_MAPS'),
          handler: () => {
            window.open('https://www.google.com/maps?q=' + latitude + ',' + longitude);
          }
        },
        {
          icon: IconsMap.waze,
          text: this.translateService.instant('WAZE'),
          handler: () => {
            window.open('https://waze.com/ul?ll=' + latitude + ',' + longitude + '&navigate=yes');
          }
        },
        {
          icon: "location-outline",
          text: this.translateService.instant("OPEN_IN_ANOTHER_APP"),
          handler: () => {
            window.open('geo:' + latitude + ',' + longitude);
          }
        }
      ];
    }
    else if (hasPostalCode) {
      locationAppsButtons = [
        {
          icon: IconsMap.googleMaps,
          text: this.translateService.instant('GOOGLE_MAPS'),
          handler: () => {
            window.open('https://www.google.com/maps?q=' + encodeURIComponent(postalCode));
          }
        }
      ];
    } 

    this.actionSheetCtrl.create({
      mode: 'ios',
      buttons: locationAppsButtons
    }).then(actSheet => actSheet.present());
  }

  toggleExpand(item:any){
    if(this.expandedItems.includes(item)){
      delete this.expandedItems[this.expandedItems.indexOf(item)]
    }
    else{
      this.expandedItems.push(item)
    }
  }
  checkType(value:any){
    return typeof value;
  }

  getItemName(itemsOrString: any, fromFooter:boolean=false): string {
    if (!itemsOrString && !fromFooter) {
      return this.translateService.instant('UNDEFINED');
    }
  
    if (typeof itemsOrString === 'string') {
      return isNaN(Number(itemsOrString)) ? itemsOrString : this.checkIfNumberAndRound(itemsOrString);
    }
  
    if (Array.isArray(itemsOrString)) {
      return itemsOrString
        .map(item =>
          item?.display_name
            ? item.display_name
            : this.translateService.instant('UNDEFINED')
        )
        .join(', ');
    }
    return this.translateService.instant('UNDEFINED');
  }
  
  getTranslatedText(itemName: string): string {
    let translatedText = '';
    this.translateService.get(itemName).subscribe((res: string) => {
      translatedText = res;
    });
    return translatedText;
  }

  getSlicedTranslatedText(itemName: string): string {
    const translatedText = this.getTranslatedText(itemName);
    return translatedText.length >= 25 ? translatedText.slice(0, 25) + '...' : translatedText;
  }
  getActionHref(actionItem:any){
    if(actionItem.hyperlink_type == 'geo'){

      return actionItem.hyperlink_type+":"+actionItem.field_1+','+actionItem.field_2
    }
    return actionItem.hyperlink_type+":"+actionItem.field_1
  }


  getSizeOfCol(section: string) {
    if (section == 'main') {
      if (this.hasSideInputs) {
        return 9;
      } else {
        return 12;
      }
    } else {
      if (this.hasSideInputs) {
        return 3;
      } else {
        return 0;
      }
    }
  }

  checkIfNumberAndRound(value: any) {
    try {
      const numberValue = parseFloat(value);
      if (!isNaN(numberValue)) {
        let decimalPlaces: number;
        if (typeof this.digits === 'string') {
          const parts = this.digits.split('.');
          if (parts.length === 2) {
            const fractionParts = parts[1].split('-');
            if (fractionParts.length >= 2) {
              decimalPlaces = parseInt(fractionParts[1], 10);
            } else {
              decimalPlaces = 2;
            }
          } else {
            decimalPlaces = 2;
          }
        } else if (typeof this.digits === 'number') {
          decimalPlaces = this.digits;
        } else {
          decimalPlaces = 2;
        }
        const factor = Math.pow(10, decimalPlaces);
        return Math.round((numberValue + Number.EPSILON) * factor) / factor;
      } else {
        return value;
      }
    } catch (error) {
      return value;
    }
  }

  shouldHideItem(item: any): boolean {
    return this.isCollapsed && !item?.keep_show_while_collapse;
  }

  shouldHideEmptyField(item: any): boolean {
    const field = item?.[1];    
    return field?.hide_empty_field === true && 
           (field.name === false || 
            field.name === undefined || 
            field.name === 'UNDEFINED' ||
            field.name === null ||
            field.name === '');
  } 
}
