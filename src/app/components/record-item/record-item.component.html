<ion-card style="height: fit-content;box-shadow: rgba(0, 0, 0, 0.19) 0px 10px 20px, rgba(0, 0, 0, 0.23) 0px 4px 4px;" [class.next]="isNext" [ngStyle]="{'margin-bottom':structuresList && structuresList.length>1?'60px':'0'}" [class.previous]="isPrevious" [class.remove-padding]="removeMargin" mode="ios">
    <div class="line-container" *ngIf="routeCollectionPage">
        <div [ngStyle]="{'background-color': redLine ? 'red':'green' }" class="line"></div>
    </div>
    <app-record-item-header [statusChanged]="statusChanged" (buttonPress)="emitButtonPress($event)" [headerInputs]="headerInputs" [currencySymbol]="recordStructureService.currencySymbol" [theme]="theme"></app-record-item-header>
    <app-record-item-content [recordCardStructure]="recordCardStructure" [digits]="digits" [tmpSequence]="tmpSequence" [showInOneLine]="showInOneLine" [hideButtons]="hideButtons" [isCollapsed]="headerInputs && headerInputs.is_collapsed" (buttonPress)="emitButtonPress($event)" [contentInputs]="contentInputs" [contentSideInputs]="contentSideInputs" [notesInputs]="NotesSectionInputs" [hasSideInputs]="hasSideInputs" [theme]="theme" [currencySymbol]="recordStructureService.currencySymbol"></app-record-item-content>
    <app-record-item-footer [buttonsToHide]="buttonsToHide" [showInOneLine]="showInOneLine" (buttonPress)="emitButtonPress($event)" [footerInputs]="footerInputs" [recordCardStructure]="recordCardStructure" [theme]="theme" [state]="state"></app-record-item-footer>
</ion-card>


<ion-fab *ngIf="structuresList && structuresList.length>1" slot="fixed" horizontal="end" vertical="bottom">
    <ion-fab-button [disabled]="!structuresList[currentIndex+1]" size="small" (click)="navigateTo('next',currentIndex+1)">
        <ion-icon  name="chevron-forward-outline"></ion-icon>
    </ion-fab-button>
</ion-fab>

<ion-fab *ngIf="structuresList && structuresList.length>1" slot="fixed" horizontal="start" vertical="bottom">
    <ion-fab-button [disabled]="!structuresList[currentIndex-1]" size="small" (click)="navigateTo('back',currentIndex-1)">
        <ion-icon  name="chevron-back-outline"></ion-icon>
    </ion-fab-button>
</ion-fab>
<div *ngIf="structuresList && structuresList.length>1" style="position: absolute; bottom: 30px; text-align: center; width: 100%;">{{currentIndex+1}} / {{structuresList.length}}</div>
