import { registerPlugin } from '@capacitor/core';

export interface ScanFromImageOptions {
  path: string;
  base64?: string;
}

export interface ScanResult {
  hasContent: boolean;
  content?: string;
  format?: string;
  cornerPoints?: { x: number; y: number }[];
  boundingBox?: { x: number; y: number; width: number; height: number };
}

export interface BarcodeScannerPlugin {
  scanFromImage(options: ScanFromImageOptions): Promise<ScanResult>;
}

const BarcodeScanner = registerPlugin<BarcodeScannerPlugin>('BarcodeScanner');

export default BarcodeScanner;
