package com.olivery.BarcodeScanner

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Rect
import android.util.Base64
import com.getcapacitor.JSObject
import com.getcapacitor.Plugin
import com.getcapacitor.PluginCall
import com.getcapacitor.PluginMethod
import com.getcapacitor.annotation.CapacitorPlugin
import com.google.mlkit.vision.barcode.BarcodeScanner
import com.google.mlkit.vision.barcode.BarcodeScannerOptions
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage
import java.util.regex.Pattern
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.Matrix
import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import kotlin.math.max
import kotlin.math.min
import kotlin.math.abs
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.PI
import kotlin.math.sqrt
import kotlin.math.pow

// ZXing imports
import com.google.zxing.BinaryBitmap
import com.google.zxing.DecodeHintType
import com.google.zxing.MultiFormatReader
import com.google.zxing.RGBLuminanceSource
import com.google.zxing.Result
import com.google.zxing.common.HybridBinarizer
import com.google.zxing.BarcodeFormat

/**
 * Interface for common barcode properties
 */
interface BarcodeInfo {
    val rawValue: String?
    val format: Int
    val boundingBox: Rect?
    val cornerPoints: Array<android.graphics.Point>?
}

/**
 * Wrapper class to hold barcode information from different sources (ML Kit or ZXing)
 */
data class BarcodeWrapper(
    override val rawValue: String?,
    override val format: Int,
    override val boundingBox: Rect?,
    override val cornerPoints: Array<android.graphics.Point>? = null
) : BarcodeInfo

/**
 * Extension to make ML Kit Barcode implement our interface
 */
class MLKitBarcodeWrapper(private val barcode: Barcode) : BarcodeInfo {
    override val rawValue: String? get() = barcode.rawValue
    override val format: Int get() = barcode.format
    override val boundingBox: Rect? get() = barcode.boundingBox
    override val cornerPoints: Array<android.graphics.Point>? get() = barcode.cornerPoints
}

@CapacitorPlugin(name = "BarcodeScanner")
class BarcodeScannerPlugin : Plugin() {

    private lateinit var barcodeScanner: BarcodeScanner

    // Error codes matching TypeScript definitions
    companion object {
        const val ERROR_UNSUPPORTED_FORMAT = "UNSUPPORTED_FORMAT"
        const val ERROR_INVALID_INPUT = "INVALID_INPUT"
        const val ERROR_NO_BARCODE_FOUND = "NO_BARCODE_FOUND"
        const val ERROR_PLATFORM_ERROR = "PLATFORM_ERROR"
        const val ERROR_BARCODE_TOO_SMALL = "BARCODE_TOO_SMALL"

        // Base64 validation pattern
        private val BASE64_PATTERN = Pattern.compile("^[A-Za-z0-9+/]*={0,2}$")

        // Maximum image size to prevent memory issues (10MB)
        private const val MAX_IMAGE_SIZE_BYTES = 10 * 1024 * 1024

        // Minimum image size for processing
        private const val MIN_IMAGE_SIZE = 32

        // Target size for image processing (balance between quality and performance)
        private const val TARGET_MAX_SIZE = 1024

        // Adaptive thresholding parameters
        private const val ADAPTIVE_THRESHOLD_BLOCK_SIZE = 51
        private const val ADAPTIVE_THRESHOLD_C = 15

        // Edge enhancement parameters
        private const val EDGE_DETECTION_THRESHOLD = 50

        // Rotation correction parameters
        private const val MAX_ROTATION_ANGLE = 45.0 // Maximum rotation angle to correct in degrees
        private const val ROTATION_ANGLE_STEP = 5.0 // Step size for rotation angle search

        // Contrast enhancement parameters
        private const val HIGH_CONTRAST_LEVEL = 2.5f
        private const val MEDIUM_CONTRAST_LEVEL = 1.8f

        // Denoising parameters
        private const val DENOISE_THRESHOLD = 20

        // Minimum barcode size as percentage of total image area
        private const val MIN_BARCODE_SIZE_PERCENTAGE = 30.0
    }

    override fun load() {
        super.load()
        try {
            // Initialize ML Kit barcode scanner with all supported formats
            val options = BarcodeScannerOptions.Builder()
                .setBarcodeFormats(
                    Barcode.FORMAT_QR_CODE,
                    Barcode.FORMAT_CODE_128,
                    Barcode.FORMAT_CODE_39,
                    Barcode.FORMAT_EAN_13,
                    Barcode.FORMAT_UPC_A,
                    Barcode.FORMAT_DATA_MATRIX,
                    Barcode.FORMAT_PDF417,
                    Barcode.FORMAT_AZTEC
                )
                .build()

            barcodeScanner = BarcodeScanning.getClient(options)
        } catch (exception: Exception) {
            // Log initialization error but don't crash
            android.util.Log.e("BarcodeScannerPlugin", "Failed to initialize ML Kit scanner", exception)
        }
    }

    /**
     * Multi-scale preprocessing optimized for small barcodes in large images
     * Includes original size processing and smart resizing
     */
    private fun preprocessImage(original: Bitmap): List<Bitmap> {
        val processedImages = mutableListOf<Bitmap>()

        // 1. ALWAYS try original size first (crucial for small barcodes)
        processedImages.add(original)

        // 2. Original grayscale (preserves full resolution)
        val originalGrayscale = createSimpleGrayscale(original)
        processedImages.add(originalGrayscale)

        // 3. Only resize if image is very large (>2048px) to preserve barcode detail
        val needsResize = max(original.width, original.height) > 2048
        if (needsResize) {
            val resized = resizeImageIfNeeded(original)

            // 4. Resized grayscale
            val resizedGrayscale = createSimpleGrayscale(resized)
            processedImages.add(resizedGrayscale)

            // 5. Sharpened resized (for edge enhancement)
            val sharpened = createSharpened(resized)
            processedImages.add(sharpened)
        } else {
            // For smaller images, apply sharpening to original size
            val sharpened = createSharpened(original)
            processedImages.add(sharpened)
        }

        // 6. Black & white with optimal thresholding (always on original size for small barcodes)
        val blackWhite = createBlackWhite(original)
        processedImages.add(blackWhite)

        return processedImages
    }

    /**
     * Creates a simple grayscale version without aggressive processing
     */
    private fun createSimpleGrayscale(input: Bitmap): Bitmap {
        val result = Bitmap.createBitmap(input.width, input.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        val paint = Paint()

        // Simple grayscale conversion without contrast adjustment
        val grayscaleMatrix = ColorMatrix().apply { setSaturation(0f) }
        paint.colorFilter = ColorMatrixColorFilter(grayscaleMatrix)
        canvas.drawBitmap(input, 0f, 0f, paint)

        return result
    }



    private fun resizeImageIfNeeded(bitmap: Bitmap): Bitmap {
        val maxDim = max(bitmap.width, bitmap.height)
        val minDim = min(bitmap.width, bitmap.height)

        // Only resize if image is very large (>2048px) to preserve barcode detail
        return if (maxDim > 2048) {
            val scale = 2048f / maxDim
            val newWidth = (bitmap.width * scale).toInt()
            val newHeight = (bitmap.height * scale).toInt()

            if (newWidth > MIN_IMAGE_SIZE && newHeight > MIN_IMAGE_SIZE) {
                Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
            } else {
                bitmap
            }
        } else if (maxDim < 400) {
            // Upscale very small images to help with barcode detection
            val scale = 400f / maxDim
            val newWidth = (bitmap.width * scale).toInt()
            val newHeight = (bitmap.height * scale).toInt()

            Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, false) // Use nearest neighbor for upscaling
        } else {
            bitmap
        }
    }





    /**
     * Creates a contrast-enhanced image with edge preservation
     */
    private fun createContrastEnhanced(input: Bitmap): Bitmap {
        val result = Bitmap.createBitmap(input.width, input.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        val paint = Paint()

        // Enhanced contrast with better brightness adjustment
        val contrast = MEDIUM_CONTRAST_LEVEL
        val brightness = 10f
        val matrix = ColorMatrix(floatArrayOf(
            contrast, 0f, 0f, 0f, brightness,
            0f, contrast, 0f, 0f, brightness,
            0f, 0f, contrast, 0f, brightness,
            0f, 0f, 0f, 1f, 0f
        ))

        paint.colorFilter = ColorMatrixColorFilter(matrix)
        canvas.drawBitmap(input, 0f, 0f, paint)

        return result
    }

    /**
     * Creates a sharpened image using convolution kernels for better barcode edge detection
     */
    private fun createSharpened(input: Bitmap): Bitmap {
        val result = Bitmap.createBitmap(input.width, input.height, Bitmap.Config.ARGB_8888)
        val pixels = IntArray(input.width * input.height)
        input.getPixels(pixels, 0, input.width, 0, 0, input.width, input.height)

        val sharpenedPixels = IntArray(pixels.size)

        // Sharpening kernel - enhances edges
        val kernel = arrayOf(
            intArrayOf(0, -1, 0),
            intArrayOf(-1, 5, -1),
            intArrayOf(0, -1, 0)
        )

        for (y in 1 until input.height - 1) {
            for (x in 1 until input.width - 1) {
                var sum = 0

                for (ky in -1..1) {
                    for (kx in -1..1) {
                        val pixelIndex = (y + ky) * input.width + (x + kx)
                        val gray = Color.red(pixels[pixelIndex])
                        sum += gray * kernel[ky + 1][kx + 1]
                    }
                }

                val sharpenedValue = sum.coerceIn(0, 255)
                val currentIndex = y * input.width + x
                sharpenedPixels[currentIndex] = Color.rgb(sharpenedValue, sharpenedValue, sharpenedValue)
            }
        }

        // Handle edges - copy original values
        for (y in 0 until input.height) {
            for (x in 0 until input.width) {
                val index = y * input.width + x
                if (y == 0 || y == input.height - 1 || x == 0 || x == input.width - 1) {
                    val gray = Color.red(pixels[index])
                    sharpenedPixels[index] = Color.rgb(gray, gray, gray)
                }
            }
        }

        result.setPixels(sharpenedPixels, 0, input.width, 0, 0, input.width, input.height)
        return result
    }

    /**
     * Creates an improved black and white image with better threshold calculation
     * Uses Otsu's method for optimal threshold determination
     */
    private fun createBlackWhite(input: Bitmap): Bitmap {
        val result = Bitmap.createBitmap(input.width, input.height, Bitmap.Config.ARGB_8888)
        val pixels = IntArray(input.width * input.height)
        input.getPixels(pixels, 0, input.width, 0, 0, input.width, input.height)

        // Calculate optimal threshold using Otsu's method approximation
        val histogram = IntArray(256)
        for (pixel in pixels) {
            val gray = Color.red(pixel)
            histogram[gray]++
        }

        val total = pixels.size
        var sum = 0
        for (i in 0..255) {
            sum += i * histogram[i]
        }

        var sumB = 0
        var wB = 0
        var maximum = 0.0
        var threshold = 0

        for (i in 0..255) {
            wB += histogram[i]
            if (wB == 0) continue

            val wF = total - wB
            if (wF == 0) break

            sumB += i * histogram[i]
            val mB = sumB.toDouble() / wB
            val mF = (sum - sumB).toDouble() / wF

            val between = wB.toDouble() * wF * (mB - mF) * (mB - mF)
            if (between > maximum) {
                maximum = between
                threshold = i
            }
        }

        // Apply threshold
        val bwPixels = IntArray(pixels.size)
        for (i in pixels.indices) {
            val gray = Color.red(pixels[i])
            val value = if (gray > threshold) 255 else 0
            bwPixels[i] = Color.rgb(value, value, value)
        }

        result.setPixels(bwPixels, 0, input.width, 0, 0, input.width, input.height)
        return result
    }

    /**
     * Rotates a bitmap by the specified angle
     */
    private fun rotateBitmap(source: Bitmap, angle: Float): Bitmap? {
        return try {
            val matrix = Matrix()
            matrix.postRotate(angle)

            val rotated = Bitmap.createBitmap(
                source, 0, 0, source.width, source.height, matrix, true
            )

            // Ensure the rotated bitmap is valid
            if (rotated.width > MIN_IMAGE_SIZE && rotated.height > MIN_IMAGE_SIZE) {
                rotated
            } else {
                rotated.recycle()
                null
            }
        } catch (e: Exception) {
            android.util.Log.w("BarcodeScannerPlugin", "Error rotating bitmap", e)
            null
        }
    }

    /**
     * Validates that the detected barcode occupies at least the minimum percentage of the image area
     * Note: QR codes are exempt from size validation as they can be effectively scanned at smaller sizes
     * @param barcode The detected barcode with bounding box information
     * @param imageWidth The width of the original image
     * @param imageHeight The height of the original image
     * @return Pair<Boolean, String> - first is validation result, second is detailed message with size info
     */
    internal fun validateBarcodeSize(barcode: Barcode, imageWidth: Int, imageHeight: Int): Pair<Boolean, String> {
        // Skip size validation for QR codes - they can be scanned effectively at smaller sizes
        if (barcode.format == Barcode.FORMAT_QR_CODE) {
            val message = "QR code '${barcode.rawValue ?: "unknown"}' - size validation skipped"
            android.util.Log.d("BarcodeScannerPlugin", message)
            return Pair(true, message)
        }

        val boundingBox = barcode.boundingBox
        if (boundingBox == null) {
            return Pair(false, "No bounding box available for barcode: ${barcode.rawValue ?: "unknown"}")
        }

        // Calculate barcode area
        val barcodeArea = boundingBox.width() * boundingBox.height()

        // Calculate total image area
        val totalImageArea = imageWidth * imageHeight

        // Calculate percentage
        val percentage = (barcodeArea.toDouble() / totalImageArea.toDouble()) * 100.0

        val detailMessage = "Barcode '${barcode.rawValue ?: "unknown"}' covers ${String.format("%.1f", percentage)}% of image (${barcodeArea}px² / ${totalImageArea}px²). Minimum required: ${MIN_BARCODE_SIZE_PERCENTAGE}%"

        android.util.Log.d("BarcodeScannerPlugin", detailMessage)

        val isValid = percentage >= MIN_BARCODE_SIZE_PERCENTAGE
        return Pair(isValid, detailMessage)
    }

    /**
     * Overloaded version for BarcodeWrapper
     */
    internal fun validateBarcodeSize(barcode: BarcodeWrapper, imageWidth: Int, imageHeight: Int): Pair<Boolean, String> {
        // Skip size validation for QR codes - they can be scanned effectively at smaller sizes
        if (barcode.format == Barcode.FORMAT_QR_CODE) {
            val message = "QR code '${barcode.rawValue ?: "unknown"}' - size validation skipped"
            android.util.Log.d("BarcodeScannerPlugin", message)
            return Pair(true, message)
        }

        val boundingBox = barcode.boundingBox
        if (boundingBox == null) {
            return Pair(false, "No bounding box available for barcode: ${barcode.rawValue ?: "unknown"}")
        }

        // Calculate barcode area
        val barcodeArea = boundingBox.width() * boundingBox.height()

        // Calculate total image area
        val totalImageArea = imageWidth * imageHeight

        // Calculate percentage
        val percentage = (barcodeArea.toDouble() / totalImageArea.toDouble()) * 100.0

        val detailMessage = "Barcode '${barcode.rawValue ?: "unknown"}' covers ${String.format("%.1f", percentage)}% of image (${barcodeArea}px² / ${totalImageArea}px²). Minimum required: ${MIN_BARCODE_SIZE_PERCENTAGE}%"

        android.util.Log.d("BarcodeScannerPlugin", detailMessage)

        val isValid = percentage >= MIN_BARCODE_SIZE_PERCENTAGE
        return Pair(isValid, detailMessage)
    }

    /**
     * Overloaded version for BarcodeInfo
     */
    internal fun validateBarcodeSize(barcode: BarcodeInfo, imageWidth: Int, imageHeight: Int): Pair<Boolean, String> {
        // Skip size validation for QR codes - they can be scanned effectively at smaller sizes
        if (barcode.format == Barcode.FORMAT_QR_CODE) {
            val message = "QR code '${barcode.rawValue ?: "unknown"}' - size validation skipped"
            android.util.Log.d("BarcodeScannerPlugin", message)
            return Pair(true, message)
        }

        val boundingBox = barcode.boundingBox
        if (boundingBox == null) {
            return Pair(false, "No bounding box available for barcode: ${barcode.rawValue ?: "unknown"}")
        }

        // Calculate barcode area
        val barcodeArea = boundingBox.width() * boundingBox.height()

        // Calculate total image area
        val totalImageArea = imageWidth * imageHeight

        // Calculate percentage
        val percentage = (barcodeArea.toDouble() / totalImageArea.toDouble()) * 100.0

        val detailMessage = "Barcode '${barcode.rawValue ?: "unknown"}' covers ${String.format("%.1f", percentage)}% of image (${barcodeArea}px² / ${totalImageArea}px²). Minimum required: ${MIN_BARCODE_SIZE_PERCENTAGE}%"

        android.util.Log.d("BarcodeScannerPlugin", detailMessage)

        val isValid = percentage >= MIN_BARCODE_SIZE_PERCENTAGE
        return Pair(isValid, detailMessage)
    }

    /**
     * Decodes barcode using ZXing library as a fallback/alternative to ML Kit
     * @param bitmap The image bitmap to decode
     * @return ZXing Result object or null if no barcode found
     */
    private fun decodeWithZXing(bitmap: Bitmap): Result? {
        return try {
            // Convert bitmap to int array
            val width = bitmap.width
            val height = bitmap.height
            val pixels = IntArray(width * height)
            bitmap.getPixels(pixels, 0, width, 0, 0, width, height)

            // Create luminance source
            val source = RGBLuminanceSource(width, height, pixels)
            val binaryBitmap = BinaryBitmap(HybridBinarizer(source))

            // Configure decoder hints for better performance and accuracy
            val hints = mapOf(
                DecodeHintType.TRY_HARDER to true,
                DecodeHintType.POSSIBLE_FORMATS to listOf(
                    BarcodeFormat.QR_CODE,
                    BarcodeFormat.CODE_128,
                    BarcodeFormat.CODE_39,
                    BarcodeFormat.EAN_13,
                    BarcodeFormat.EAN_8,
                    BarcodeFormat.UPC_A,
                    BarcodeFormat.UPC_E,
                    BarcodeFormat.DATA_MATRIX,
                    BarcodeFormat.PDF_417,
                    BarcodeFormat.AZTEC
                )
            )

            // Create reader and decode
            val reader = MultiFormatReader()
            reader.setHints(hints)
            val result = reader.decode(binaryBitmap)

            android.util.Log.d("BarcodeScannerPlugin",
                "ZXing decoded: ${result.text} (format: ${result.barcodeFormat})")

            result
        } catch (e: Exception) {
            android.util.Log.d("BarcodeScannerPlugin", "ZXing decode failed: ${e.message}")
            null
        }
    }

    /**
     * Tries ZXing as a fallback decoder when ML Kit fails
     */
    private fun tryZXingFallback(
        bitmap: Bitmap,
        imageIndex: Int,
        techniqueName: String,
        call: PluginCall,
        originalImageWidth: Int,
        originalImageHeight: Int,
        originalImages: MutableList<Bitmap>,
        allBarcodes: MutableList<BarcodeInfo>,
        barcodesDetectedButTooSmallRef: Array<Boolean>
    ) {
        try {
            val zxingResult = decodeWithZXing(bitmap)
            if (zxingResult != null) {
                android.util.Log.d("BarcodeScannerPlugin",
                    "ZXing fallback successful for $techniqueName: ${zxingResult.text}")

                // Convert ZXing result to BarcodeWrapper format
                val mockBarcode = createBarcodeWrapperFromZXing(zxingResult)

                // Validate barcode size (same as ML Kit flow)
                val (isValid, detailMessage) = validateBarcodeSize(mockBarcode, originalImageWidth, originalImageHeight)
                if (isValid) {
                    // Add to collection for potential return
                    allBarcodes.add(mockBarcode)
                    android.util.Log.d("BarcodeScannerPlugin",
                        "ZXing barcode accepted: ${zxingResult.text}")
                } else {
                    barcodesDetectedButTooSmallRef[0] = true
                    android.util.Log.d("BarcodeScannerPlugin",
                        "ZXing barcode rejected: $detailMessage")
                }
            }
        } catch (e: Exception) {
            android.util.Log.w("BarcodeScannerPlugin", "ZXing fallback failed for $techniqueName", e)
        }
    }

    /**
     * Creates a BarcodeWrapper from ZXing Result for consistency with ML Kit results
     */
    private fun createBarcodeWrapperFromZXing(zxingResult: Result): BarcodeWrapper {
        // ZXing provides result points, convert to bounding box if available
        val boundingBox = zxingResult.resultPoints?.let { points ->
            if (points.isNotEmpty()) {
                val minX = points.minOf { it.x }.toInt()
                val maxX = points.maxOf { it.x }.toInt()
                val minY = points.minOf { it.y }.toInt()
                val maxY = points.maxOf { it.y }.toInt()
                Rect(minX, minY, maxX, maxY)
            } else {
                // If no points available, create a default bounding box
                Rect(0, 0, 100, 100)
            }
        }

        // Convert result points to corner points if available
        val cornerPoints = zxingResult.resultPoints?.map { point ->
            android.graphics.Point(point.x.toInt(), point.y.toInt())
        }?.toTypedArray()

        return BarcodeWrapper(
            rawValue = zxingResult.text,
            format = convertZXingFormatToMLKit(zxingResult.barcodeFormat),
            boundingBox = boundingBox,
            cornerPoints = cornerPoints
        )
    }

    /**
     * Converts ZXing BarcodeFormat to ML Kit format constants
     */
    private fun convertZXingFormatToMLKit(zxingFormat: BarcodeFormat): Int {
        return when (zxingFormat) {
            BarcodeFormat.QR_CODE -> Barcode.FORMAT_QR_CODE
            BarcodeFormat.CODE_128 -> Barcode.FORMAT_CODE_128
            BarcodeFormat.CODE_39 -> Barcode.FORMAT_CODE_39
            BarcodeFormat.EAN_13 -> Barcode.FORMAT_EAN_13
            BarcodeFormat.EAN_8 -> Barcode.FORMAT_EAN_8
            BarcodeFormat.UPC_A -> Barcode.FORMAT_UPC_A
            BarcodeFormat.UPC_E -> Barcode.FORMAT_UPC_E
            BarcodeFormat.DATA_MATRIX -> Barcode.FORMAT_DATA_MATRIX
            BarcodeFormat.PDF_417 -> Barcode.FORMAT_PDF417
            BarcodeFormat.AZTEC -> Barcode.FORMAT_AZTEC
            else -> Barcode.FORMAT_UNKNOWN
        }
    }








    override fun handleOnDestroy() {
        super.handleOnDestroy()
        // Clean up resources
        try {
            if (::barcodeScanner.isInitialized) {
                barcodeScanner.close()
            }
        } catch (exception: Exception) {
            android.util.Log.w("BarcodeScannerPlugin", "Error closing barcode scanner", exception)
        }
    }

    @PluginMethod
    fun scanFromImage(call: PluginCall) {
        val base64Image = call.getString("base64")

        // Comprehensive input validation
        val validationError = validateInput(base64Image)
        if (validationError != null) {
            rejectWithError(call, validationError.first, validationError.second)
            return
        }

        // Check if scanner is initialized
        if (!::barcodeScanner.isInitialized) {
            rejectWithError(call, ERROR_PLATFORM_ERROR, "Barcode scanner not initialized")
            return
        }

        var originalBitmap: Bitmap? = null
        val processedBitmaps = mutableListOf<Bitmap>()

        try {
            // Convert base64 to Bitmap with enhanced validation
            originalBitmap = base64ToBitmap(base64Image!!)
            if (originalBitmap == null) {
                rejectWithError(call, ERROR_INVALID_INPUT, "Failed to decode base64 image data")
                return
            }

            // Validate bitmap properties
            if (originalBitmap.isRecycled) {
                rejectWithError(call, ERROR_INVALID_INPUT, "Image bitmap is recycled")
                return
            }

            if (originalBitmap.width <= 0 || originalBitmap.height <= 0) {
                rejectWithError(call, ERROR_INVALID_INPUT, "Invalid image dimensions")
                return
            }

            // Process image with minimal enhancement techniques for speed
            val enhancedImages = preprocessImage(originalBitmap)
            processedBitmaps.addAll(enhancedImages)

            // Try scanning with each processed image
            scanWithMultipleImages(call, processedBitmaps, originalBitmap.width, originalBitmap.height)

        } catch (exception: Exception) {
            // Clean up bitmaps on exception
            cleanupBitmaps(originalBitmap, processedBitmaps)

            val errorMessage = when (exception) {
                is OutOfMemoryError -> "Insufficient memory to process image"
                is IllegalArgumentException -> "Invalid input parameters: ${exception.message}"
                else -> "Error processing barcode: ${exception.message}"
            }

            rejectWithError(call, ERROR_PLATFORM_ERROR, errorMessage)
        }
    }

    private fun scanWithMultipleImages(call: PluginCall, images: List<Bitmap>, originalImageWidth: Int, originalImageHeight: Int) {
        var currentImageIndex = 0
        val allBarcodes = mutableListOf<BarcodeInfo>()
        var rotationImagesAdded = false
        val originalImages = images.toMutableList()
        var barcodesDetectedButTooSmall = false
        var lastRejectedBarcodeMessage = ""
        val barcodesDetectedButTooSmallRef = arrayOf(barcodesDetectedButTooSmall)

        fun processNextImage() {
            // If we've tried all main processing techniques and found nothing, try ONE rotation angle
            if (currentImageIndex >= originalImages.size && !rotationImagesAdded && allBarcodes.isEmpty()) {
                // Only try one rotation angle (-5 degrees is most common) on original size
                val rotated = rotateBitmap(originalImages[0], -5f)
                if (rotated != null) {
                    originalImages.add(rotated)
                }
                rotationImagesAdded = true
            }

            if (currentImageIndex >= originalImages.size) {
                // All images processed, return best result
                cleanupBitmaps(null, originalImages)

                if (allBarcodes.isEmpty()) {
                    // Final attempt with ZXing on original image
                    android.util.Log.d("BarcodeScannerPlugin", "Final ZXing attempt on original image")
                    val finalZXingResult = decodeWithZXing(originalImages[0])
                    if (finalZXingResult != null) {
                        val mockBarcode = createBarcodeWrapperFromZXing(finalZXingResult)
                        val (isValid, detailMessage) = validateBarcodeSize(mockBarcode, originalImageWidth, originalImageHeight)
                        if (isValid) {
                            android.util.Log.d("BarcodeScannerPlugin",
                                "Final ZXing attempt successful: ${finalZXingResult.text}")
                            val result = createBarcodeResult(mockBarcode)
                            call.resolve(result)
                            return
                        } else {
                            barcodesDetectedButTooSmallRef[0] = true
                            lastRejectedBarcodeMessage = detailMessage
                        }
                    }

                    if (barcodesDetectedButTooSmall || barcodesDetectedButTooSmallRef[0]) {
                        val errorMessage = if (lastRejectedBarcodeMessage.isNotEmpty()) {
                            "Get closer to the barcode. $lastRejectedBarcodeMessage"
                        } else {
                            "Get closer to the barcode"
                        }
                        rejectWithError(call, ERROR_BARCODE_TOO_SMALL, errorMessage)
                    } else {
                        rejectWithError(call, ERROR_NO_BARCODE_FOUND, "No barcode detected in the provided image")
                    }
                } else {
                    // Return the barcode with highest confidence or first valid one
                    val bestBarcode = selectBestBarcode(allBarcodes)
                    val result = createBarcodeResult(bestBarcode)
                    call.resolve(result)
                }
                return
            }

            val currentBitmap = originalImages[currentImageIndex]

            // Minimal logging for performance
            val techniqueName = when (currentImageIndex) {
                0 -> "Original Full Size"
                1 -> "Original Grayscale"
                2 -> "Processed"
                3 -> "Sharpened"
                4 -> "Black & White"
                5 -> "Rotation"
                else -> "Unknown"
            }

            try {
                // Create InputImage from current Bitmap
                val inputImage = InputImage.fromBitmap(currentBitmap, 0)

                // Process image with ML Kit
                barcodeScanner.process(inputImage)
                    .addOnSuccessListener { barcodes ->
                        try {
                            // Filter barcodes by size validation and add valid ones to our collection
                            val validBarcodes = mutableListOf<Barcode>()
                            barcodes.forEach { barcode ->
                                if (!barcode.rawValue.isNullOrEmpty()) {
                                    // Validate barcode size
                                    val (isValid, detailMessage) = validateBarcodeSize(barcode, originalImageWidth, originalImageHeight)
                                    if (isValid) {
                                        allBarcodes.add(MLKitBarcodeWrapper(barcode))
                                        validBarcodes.add(barcode)
                                    } else {
                                        barcodesDetectedButTooSmall = true
                                        lastRejectedBarcodeMessage = detailMessage
                                        android.util.Log.d("BarcodeScannerPlugin",
                                            "Barcode rejected: $detailMessage")
                                    }
                                }
                            }

                            // If we found valid barcodes in high-priority images, return immediately
                            if (validBarcodes.isNotEmpty() && isHighConfidenceImage(currentImageIndex)) {
                                // Found valid barcode - return immediately for speed
                                cleanupBitmaps(null, originalImages)
                                val result = createBarcodeResult(selectBestBarcode(validBarcodes))
                                call.resolve(result)
                                return@addOnSuccessListener
                            }

                            // Continue to next image
                            currentImageIndex++
                            processNextImage()

                        } catch (exception: Exception) {
                            android.util.Log.w("BarcodeScannerPlugin", "Error processing barcode results", exception)
                            currentImageIndex++
                            processNextImage()
                        }
                    }
                    .addOnFailureListener { exception ->
                        android.util.Log.w("BarcodeScannerPlugin", "ML Kit processing failed for image $currentImageIndex", exception)

                        // Try ZXing as fallback when ML Kit fails
                        tryZXingFallback(
                            currentBitmap,
                            currentImageIndex,
                            techniqueName,
                            call,
                            originalImageWidth,
                            originalImageHeight,
                            originalImages,
                            allBarcodes,
                            barcodesDetectedButTooSmallRef
                        )

                        currentImageIndex++
                        processNextImage()
                    }

            } catch (exception: Exception) {
                android.util.Log.w("BarcodeScannerPlugin", "Error creating InputImage for index $currentImageIndex", exception)
                currentImageIndex++
                processNextImage()
            }
        }

        // Start processing
        processNextImage()
    }

    /**
     * Determines if the current image index represents a high-confidence preprocessing technique
     * Prioritizes original size processing for small barcodes
     */
    private fun isHighConfidenceImage(imageIndex: Int): Boolean {
        return imageIndex <= 4  // Original, original grayscale, resized versions, sharpened, and black & white
    }

    private fun selectBestBarcode(barcodes: List<Barcode>): Barcode {
        if (barcodes.size == 1) return barcodes[0]

        // Enhanced barcode selection with multiple criteria
        // CODE39 and CODE128 are given highest priority
        val formatPriority = mapOf(
            Barcode.FORMAT_CODE_39 to 1,  // Highest priority
            Barcode.FORMAT_CODE_128 to 1, // Equal highest priority
            Barcode.FORMAT_EAN_13 to 3,
            Barcode.FORMAT_UPC_A to 4,
            Barcode.FORMAT_QR_CODE to 5,
            Barcode.FORMAT_DATA_MATRIX to 6,
            Barcode.FORMAT_PDF417 to 7,
            Barcode.FORMAT_AZTEC to 8
        )

        return barcodes.minByOrNull { barcode ->
            val formatScore = formatPriority[barcode.format] ?: 99
            val lengthScore = if (barcode.rawValue?.length ?: 0 > 0) 0 else 10

            // Prefer barcodes with bounding boxes (better detection confidence)
            val boundingBoxScore = if (barcode.boundingBox != null) 0 else 5

            // Prefer barcodes with reasonable content length
            val contentLength = barcode.rawValue?.length ?: 0
            val contentScore = when {
                contentLength == 0 -> 20
                contentLength < 3 -> 10
                contentLength in 3..50 -> 0
                else -> 2
            }

            formatScore + lengthScore + boundingBoxScore + contentScore
        } ?: barcodes[0]
    }

    /**
     * Overloaded version for BarcodeInfo
     */
    private fun selectBestBarcode(barcodes: List<BarcodeInfo>): BarcodeInfo {
        if (barcodes.size == 1) return barcodes[0]

        // Enhanced barcode selection with multiple criteria
        // CODE39 and CODE128 are given highest priority
        val formatPriority = mapOf(
            Barcode.FORMAT_CODE_39 to 1,  // Highest priority
            Barcode.FORMAT_CODE_128 to 1, // Equal highest priority
            Barcode.FORMAT_EAN_13 to 3,
            Barcode.FORMAT_UPC_A to 4,
            Barcode.FORMAT_QR_CODE to 5,
            Barcode.FORMAT_DATA_MATRIX to 6,
            Barcode.FORMAT_PDF417 to 7,
            Barcode.FORMAT_AZTEC to 8
        )

        return barcodes.minByOrNull { barcode ->
            val formatScore = formatPriority[barcode.format] ?: 99
            val lengthScore = if (barcode.rawValue?.length ?: 0 > 0) 0 else 10

            // Prefer barcodes with bounding boxes (better detection confidence)
            val boundingBoxScore = if (barcode.boundingBox != null) 0 else 5

            // Prefer barcodes with reasonable content length
            val contentLength = barcode.rawValue?.length ?: 0
            val contentScore = when {
                contentLength == 0 -> 20
                contentLength < 3 -> 10
                contentLength in 3..50 -> 0
                else -> 2
            }

            formatScore + lengthScore + boundingBoxScore + contentScore
        } ?: barcodes[0]
    }

    private fun cleanupBitmaps(original: Bitmap?, processed: List<Bitmap>) {
        original?.let { bmp ->
            if (!bmp.isRecycled) {
                bmp.recycle()
            }
        }

        processed.forEach { bitmap ->
            if (!bitmap.isRecycled) {
                bitmap.recycle()
            }
        }
    }

    private fun validateInput(base64Image: String?): Pair<String, String>? {
        // Check if input is null or empty
        if (base64Image.isNullOrEmpty()) {
            return Pair(ERROR_INVALID_INPUT, "Base64 image data is required")
        }

        // Check minimum length (a valid base64 image should be reasonably long)
        if (base64Image.length < 20) {
            return Pair(ERROR_INVALID_INPUT, "Base64 image data is too short")
        }

        // Extract clean base64 string
        val cleanBase64 = if (base64Image.contains(",")) {
            val parts = base64Image.split(",")
            if (parts.size != 2) {
                return Pair(ERROR_INVALID_INPUT, "Invalid data URL format")
            }

            // Validate data URL prefix
            val prefix = parts[0].lowercase()
            if (!prefix.startsWith("data:image/") || !prefix.contains("base64")) {
                return Pair(ERROR_UNSUPPORTED_FORMAT, "Unsupported image format in data URL")
            }

            parts[1]
        } else {
            base64Image
        }

        // Validate base64 format
        if (!BASE64_PATTERN.matcher(cleanBase64).matches()) {
            return Pair(ERROR_INVALID_INPUT, "Invalid base64 format")
        }

        // Check estimated size to prevent memory issues
        val estimatedSize = (cleanBase64.length * 3) / 4
        if (estimatedSize > MAX_IMAGE_SIZE_BYTES) {
            return Pair(ERROR_INVALID_INPUT, "Image size exceeds maximum allowed size")
        }

        return null // No validation errors
    }

    private fun rejectWithError(call: PluginCall, errorCode: String, message: String) {
        val errorObject = JSObject()
        errorObject.put("code", errorCode)
        errorObject.put("message", message)
        call.reject(message, errorCode, null, errorObject)
    }

    private fun base64ToBitmap(base64String: String): Bitmap? {
        return try {
            // Remove data URL prefix if present (e.g., "data:image/png;base64,")
            val cleanBase64 = if (base64String.contains(",")) {
                base64String.substring(base64String.indexOf(",") + 1)
            } else {
                base64String
            }

            // Validate base64 string length after cleaning
            if (cleanBase64.isEmpty()) {
                return null
            }

            val decodedBytes = try {
                Base64.decode(cleanBase64, Base64.DEFAULT)
            } catch (exception: IllegalArgumentException) {
                // Invalid base64 format
                return null
            }

            // Check if decoded bytes are reasonable
            if (decodedBytes.isEmpty() || decodedBytes.size < 10) {
                return null
            }

            // Decode to bitmap with better options for quality
            val options = BitmapFactory.Options().apply {
                inPreferredConfig = Bitmap.Config.ARGB_8888
                inMutable = true
                inSampleSize = 1 // Don't downsample initially
            }

            val bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size, options)

            // Validate bitmap was created successfully
            if (bitmap == null) {
                return null
            }

            // Additional bitmap validation
            if (bitmap.width <= 0 || bitmap.height <= 0) {
                bitmap.recycle()
                return null
            }

            bitmap
        } catch (exception: Exception) {
            android.util.Log.w("BarcodeScannerPlugin", "Error converting base64 to bitmap", exception)
            null
        }
    }

    private fun createBarcodeResult(barcode: Barcode): JSObject {
        val result = JSObject()

        // Extract barcode value
        val rawValue = barcode.rawValue ?: ""
        if (rawValue.isNotEmpty()) {
            result.put("content", rawValue)
            result.put("hasContent", true)
        } else {
            result.put("hasContent", false)
            result.put("content", "")
        }

        // Map ML Kit format to our format enum
        result.put("format", mapBarcodeFormat(barcode.format))

        // Extract bounding box
        val boundingBox = JSObject()
        barcode.boundingBox?.let { rect ->
            boundingBox.put("x", rect.left)
            boundingBox.put("y", rect.top)
            boundingBox.put("width", rect.width())
            boundingBox.put("height", rect.height())
        }
        result.put("boundingBox", boundingBox)

        // Extract corner points
        val cornerPointsArray = mutableListOf<JSObject>()
        barcode.cornerPoints?.let { points ->
            for (point in points) {
                val cornerPoint = JSObject()
                cornerPoint.put("x", point.x)
                cornerPoint.put("y", point.y)
                cornerPointsArray.add(cornerPoint)
            }
        }
        result.put("cornerPoints", cornerPointsArray)

        return result
    }

    /**
     * Overloaded version for BarcodeWrapper
     */
    private fun createBarcodeResult(barcode: BarcodeWrapper): JSObject {
        val result = JSObject()

        // Extract barcode value
        val rawValue = barcode.rawValue ?: ""
        if (rawValue.isNotEmpty()) {
            result.put("content", rawValue)
            result.put("hasContent", true)
        } else {
            result.put("hasContent", false)
            result.put("content", "")
        }

        // Map ML Kit format to our format enum
        result.put("format", mapBarcodeFormat(barcode.format))

        // Extract bounding box
        val boundingBox = JSObject()
        barcode.boundingBox?.let { rect ->
            boundingBox.put("x", rect.left)
            boundingBox.put("y", rect.top)
            boundingBox.put("width", rect.width())
            boundingBox.put("height", rect.height())
        }
        result.put("boundingBox", boundingBox)

        // Extract corner points
        val cornerPointsArray = mutableListOf<JSObject>()
        barcode.cornerPoints?.let { points ->
            for (point in points) {
                val cornerPoint = JSObject()
                cornerPoint.put("x", point.x)
                cornerPoint.put("y", point.y)
                cornerPointsArray.add(cornerPoint)
            }
        }
        result.put("cornerPoints", cornerPointsArray)

        return result
    }

    /**
     * Overloaded version for BarcodeInfo
     */
    private fun createBarcodeResult(barcode: BarcodeInfo): JSObject {
        val result = JSObject()

        // Extract barcode value
        val rawValue = barcode.rawValue ?: ""
        if (rawValue.isNotEmpty()) {
            result.put("content", rawValue)
            result.put("hasContent", true)
        } else {
            result.put("hasContent", false)
            result.put("content", "")
        }

        // Map ML Kit format to our format enum
        result.put("format", mapBarcodeFormat(barcode.format))

        // Extract bounding box
        val boundingBox = JSObject()
        barcode.boundingBox?.let { rect ->
            boundingBox.put("x", rect.left)
            boundingBox.put("y", rect.top)
            boundingBox.put("width", rect.width())
            boundingBox.put("height", rect.height())
        }
        result.put("boundingBox", boundingBox)

        // Extract corner points
        val cornerPointsArray = mutableListOf<JSObject>()
        barcode.cornerPoints?.let { points ->
            for (point in points) {
                val cornerPoint = JSObject()
                cornerPoint.put("x", point.x)
                cornerPoint.put("y", point.y)
                cornerPointsArray.add(cornerPoint)
            }
        }
        result.put("cornerPoints", cornerPointsArray)

        return result
    }

    private fun mapBarcodeFormat(mlKitFormat: Int): String {
        return when (mlKitFormat) {
            Barcode.FORMAT_QR_CODE -> "QR_CODE"
            Barcode.FORMAT_CODE_128 -> "CODE_128"
            Barcode.FORMAT_CODE_39 -> "CODE_39"
            Barcode.FORMAT_EAN_13 -> "EAN_13"
            Barcode.FORMAT_UPC_A -> "UPC_A"
            Barcode.FORMAT_DATA_MATRIX -> "DATA_MATRIX"
            Barcode.FORMAT_PDF417 -> "PDF417"
            Barcode.FORMAT_AZTEC -> "AZTEC"
            else -> "UNKNOWN"
        }
    }
}
