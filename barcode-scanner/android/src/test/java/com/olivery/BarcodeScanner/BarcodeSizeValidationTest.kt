package com.olivery.BarcodeScanner

import android.graphics.Rect
import com.google.mlkit.vision.barcode.common.Barcode
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mockito.*

/**
 * Unit tests for barcode size validation functionality
 */
class BarcodeSizeValidationTest {

    /**
     * Test that a barcode occupying exactly 50% of the image area is considered valid
     */
    @Test
    fun testBarcodeExactly50PercentIsValid() {
        // Create a mock barcode with bounding box
        val mockBarcode = mock(Barcode::class.java)
        val boundingBox = Rect(0, 0, 500, 600) // 300,000 pixels
        `when`(mockBarcode.boundingBox).thenReturn(boundingBox)

        // Image dimensions: 1000x600 = 600,000 pixels
        // Barcode area: 500x600 = 300,000 pixels
        // Percentage: 300,000 / 600,000 = 50%
        val imageWidth = 1000
        val imageHeight = 600

        // Create plugin instance and test validation
        val plugin = BarcodeScannerPlugin()
        val (isValid, message) = plugin.validateBarcodeSize(mockBarcode, imageWidth, imageHeight)

        assertTrue("Barcode with exactly 50% coverage should be valid", isValid)
        assertTrue("Message should contain percentage info", message.contains("50.0%"))
    }

    /**
     * Test that a barcode occupying more than 50% of the image area is considered valid
     */
    @Test
    fun testBarcodeOver50PercentIsValid() {
        val mockBarcode = mock(Barcode::class.java)
        val boundingBox = Rect(0, 0, 900, 600) // 540,000 pixels
        `when`(mockBarcode.boundingBox).thenReturn(boundingBox)

        // Image dimensions: 1000x600 = 600,000 pixels
        // Barcode area: 900x600 = 540,000 pixels
        // Percentage: 540,000 / 600,000 = 90%
        val imageWidth = 1000
        val imageHeight = 600

        val plugin = BarcodeScannerPlugin()
        val (isValid, message) = plugin.validateBarcodeSize(mockBarcode, imageWidth, imageHeight)

        assertTrue("Barcode with 90% coverage should be valid", isValid)
        assertTrue("Message should contain percentage info", message.contains("90.0%"))
    }

    /**
     * Test that a barcode occupying less than 50% of the image area is considered invalid
     */
    @Test
    fun testBarcodeUnder50PercentIsInvalid() {
        val mockBarcode = mock(Barcode::class.java)
        val boundingBox = Rect(0, 0, 400, 600) // 240,000 pixels
        `when`(mockBarcode.boundingBox).thenReturn(boundingBox)

        // Image dimensions: 1000x600 = 600,000 pixels
        // Barcode area: 400x600 = 240,000 pixels
        // Percentage: 240,000 / 600,000 = 40%
        val imageWidth = 1000
        val imageHeight = 600

        val plugin = BarcodeScannerPlugin()
        val (isValid, message) = plugin.validateBarcodeSize(mockBarcode, imageWidth, imageHeight)

        assertFalse("Barcode with 40% coverage should be invalid", isValid)
        assertTrue("Message should contain percentage info", message.contains("40.0%"))
    }

    /**
     * Test that a barcode with null bounding box is considered invalid
     */
    @Test
    fun testBarcodeWithNullBoundingBoxIsInvalid() {
        val mockBarcode = mock(Barcode::class.java)
        `when`(mockBarcode.boundingBox).thenReturn(null)
        
        val imageWidth = 1000
        val imageHeight = 600
        
        val plugin = BarcodeScannerPlugin()
        val (isValid, message) = plugin.validateBarcodeSize(mockBarcode, imageWidth, imageHeight)

        assertFalse("Barcode with null bounding box should be invalid", isValid)
        assertTrue("Message should mention no bounding box", message.contains("No bounding box"))
    }

    /**
     * Test edge case with very small barcode in large image
     */
    @Test
    fun testVerySmallBarcodeIsInvalid() {
        val mockBarcode = mock(Barcode::class.java)
        val boundingBox = Rect(0, 0, 100, 100) // 10,000 pixels
        `when`(mockBarcode.boundingBox).thenReturn(boundingBox)
        
        // Image dimensions: 2000x2000 = 4,000,000 pixels
        // Barcode area: 100x100 = 10,000 pixels
        // Percentage: 10,000 / 4,000,000 = 0.25%
        val imageWidth = 2000
        val imageHeight = 2000
        
        val plugin = BarcodeScannerPlugin()
        val (isValid, message) = plugin.validateBarcodeSize(mockBarcode, imageWidth, imageHeight)

        assertFalse("Very small barcode (0.25% coverage) should be invalid", isValid)
        assertTrue("Message should contain percentage info", message.contains("0.3%"))
    }

    /**
     * Test edge case with barcode covering entire image
     */
    @Test
    fun testFullImageBarcodeIsValid() {
        val mockBarcode = mock(Barcode::class.java)
        val boundingBox = Rect(0, 0, 1000, 600) // 600,000 pixels
        `when`(mockBarcode.boundingBox).thenReturn(boundingBox)
        
        // Image dimensions: 1000x600 = 600,000 pixels
        // Barcode area: 1000x600 = 600,000 pixels
        // Percentage: 600,000 / 600,000 = 100%
        val imageWidth = 1000
        val imageHeight = 600
        
        val plugin = BarcodeScannerPlugin()
        val (isValid, message) = plugin.validateBarcodeSize(mockBarcode, imageWidth, imageHeight)

        assertTrue("Barcode covering entire image (100% coverage) should be valid", isValid)
        assertTrue("Message should contain percentage info", message.contains("100.0%"))
    }
}
