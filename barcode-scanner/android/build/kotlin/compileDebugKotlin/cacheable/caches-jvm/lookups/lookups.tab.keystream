  Bitmap android.graphics  
BitmapFactory android.graphics  Canvas android.graphics  Color android.graphics  ColorFilter android.graphics  ColorMatrix android.graphics  ColorMatrixColorFilter android.graphics  Matrix android.graphics  Paint android.graphics  Point android.graphics  
PorterDuff android.graphics  PorterDuffXfermode android.graphics  Rect android.graphics  RectF android.graphics  Config android.graphics.Bitmap  createBitmap android.graphics.Bitmap  createScaledBitmap android.graphics.Bitmap  equals android.graphics.Bitmap  	getHEIGHT android.graphics.Bitmap  	getHeight android.graphics.Bitmap  
getISRecycled android.graphics.Bitmap  
getIsRecycled android.graphics.Bitmap  getLET android.graphics.Bitmap  getLet android.graphics.Bitmap  	getPixels android.graphics.Bitmap  getWIDTH android.graphics.Bitmap  getWidth android.graphics.Bitmap  height android.graphics.Bitmap  
isRecycled android.graphics.Bitmap  let android.graphics.Bitmap  recycle android.graphics.Bitmap  	setHeight android.graphics.Bitmap  	setPixels android.graphics.Bitmap  setRecycled android.graphics.Bitmap  setWidth android.graphics.Bitmap  width android.graphics.Bitmap  	ARGB_8888 android.graphics.Bitmap.Config  Options android.graphics.BitmapFactory  decodeByteArray android.graphics.BitmapFactory  Bitmap &android.graphics.BitmapFactory.Options  apply &android.graphics.BitmapFactory.Options  getAPPLY &android.graphics.BitmapFactory.Options  getApply &android.graphics.BitmapFactory.Options  	inMutable &android.graphics.BitmapFactory.Options  inPreferredConfig &android.graphics.BitmapFactory.Options  inSampleSize &android.graphics.BitmapFactory.Options  
drawBitmap android.graphics.Canvas  red android.graphics.Color  rgb android.graphics.Color  apply android.graphics.ColorMatrix  getAPPLY android.graphics.ColorMatrix  getApply android.graphics.ColorMatrix  
setSaturation android.graphics.ColorMatrix  
postRotate android.graphics.Matrix  colorFilter android.graphics.Paint  getCOLORFilter android.graphics.Paint  getColorFilter android.graphics.Paint  setColorFilter android.graphics.Paint  x android.graphics.Point  y android.graphics.Point  equals android.graphics.Rect  getLET android.graphics.Rect  getLet android.graphics.Rect  height android.graphics.Rect  left android.graphics.Rect  let android.graphics.Rect  top android.graphics.Rect  width android.graphics.Rect  Base64 android.util  Log android.util  DEFAULT android.util.Base64  decode android.util.Base64  e android.util.Log  w android.util.Log  JSObject com.getcapacitor  Plugin com.getcapacitor  
PluginCall com.getcapacitor  PluginMethod com.getcapacitor  put com.getcapacitor.JSObject  BASE64_PATTERN com.getcapacitor.Plugin  Barcode com.getcapacitor.Plugin  BarcodeScanner com.getcapacitor.Plugin  BarcodeScannerOptions com.getcapacitor.Plugin  BarcodeScanning com.getcapacitor.Plugin  Base64 com.getcapacitor.Plugin  Bitmap com.getcapacitor.Plugin  
BitmapFactory com.getcapacitor.Plugin  Boolean com.getcapacitor.Plugin  Canvas com.getcapacitor.Plugin  Color com.getcapacitor.Plugin  ColorMatrix com.getcapacitor.Plugin  ColorMatrixColorFilter com.getcapacitor.Plugin  ERROR_INVALID_INPUT com.getcapacitor.Plugin  ERROR_NO_BARCODE_FOUND com.getcapacitor.Plugin  ERROR_PLATFORM_ERROR com.getcapacitor.Plugin  ERROR_UNSUPPORTED_FORMAT com.getcapacitor.Plugin  	Exception com.getcapacitor.Plugin  Float com.getcapacitor.Plugin  IllegalArgumentException com.getcapacitor.Plugin  
InputImage com.getcapacitor.Plugin  Int com.getcapacitor.Plugin  IntArray com.getcapacitor.Plugin  JSObject com.getcapacitor.Plugin  List com.getcapacitor.Plugin  MAX_IMAGE_SIZE_BYTES com.getcapacitor.Plugin  MEDIUM_CONTRAST_LEVEL com.getcapacitor.Plugin  MIN_IMAGE_SIZE com.getcapacitor.Plugin  Matrix com.getcapacitor.Plugin  OutOfMemoryError com.getcapacitor.Plugin  Paint com.getcapacitor.Plugin  Pair com.getcapacitor.Plugin  Pattern com.getcapacitor.Plugin  
PluginCall com.getcapacitor.Plugin  PluginMethod com.getcapacitor.Plugin  String com.getcapacitor.Plugin  android com.getcapacitor.Plugin  apply com.getcapacitor.Plugin  arrayOf com.getcapacitor.Plugin  barcodeScanner com.getcapacitor.Plugin  base64ToBitmap com.getcapacitor.Plugin  cleanupBitmaps com.getcapacitor.Plugin  coerceIn com.getcapacitor.Plugin  contains com.getcapacitor.Plugin  createBarcodeResult com.getcapacitor.Plugin  createBlackWhite com.getcapacitor.Plugin  createSharpened com.getcapacitor.Plugin  createSimpleGrayscale com.getcapacitor.Plugin  floatArrayOf com.getcapacitor.Plugin  handleOnDestroy com.getcapacitor.Plugin  indexOf com.getcapacitor.Plugin  indices com.getcapacitor.Plugin  
intArrayOf com.getcapacitor.Plugin  isEmpty com.getcapacitor.Plugin  isHighConfidenceImage com.getcapacitor.Plugin  
isInitialized com.getcapacitor.Plugin  
isNotEmpty com.getcapacitor.Plugin  
isNullOrEmpty com.getcapacitor.Plugin  let com.getcapacitor.Plugin  load com.getcapacitor.Plugin  	lowercase com.getcapacitor.Plugin  mapBarcodeFormat com.getcapacitor.Plugin  mapOf com.getcapacitor.Plugin  max com.getcapacitor.Plugin  min com.getcapacitor.Plugin  minByOrNull com.getcapacitor.Plugin  
mutableListOf com.getcapacitor.Plugin  
plusAssign com.getcapacitor.Plugin  preprocessImage com.getcapacitor.Plugin  rejectWithError com.getcapacitor.Plugin  resizeImageIfNeeded com.getcapacitor.Plugin  rotateBitmap com.getcapacitor.Plugin  scanWithMultipleImages com.getcapacitor.Plugin  selectBestBarcode com.getcapacitor.Plugin  split com.getcapacitor.Plugin  
startsWith com.getcapacitor.Plugin  	substring com.getcapacitor.Plugin  to com.getcapacitor.Plugin  
toMutableList com.getcapacitor.Plugin  until com.getcapacitor.Plugin  
validateInput com.getcapacitor.Plugin  	getString com.getcapacitor.PluginCall  reject com.getcapacitor.PluginCall  resolve com.getcapacitor.PluginCall  CapacitorPlugin com.getcapacitor.annotation  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  BarcodeScanner com.google.mlkit.vision.barcode  BarcodeScannerOptions com.google.mlkit.vision.barcode  BarcodeScanning com.google.mlkit.vision.barcode  close .com.google.mlkit.vision.barcode.BarcodeScanner  process .com.google.mlkit.vision.barcode.BarcodeScanner  Builder 5com.google.mlkit.vision.barcode.BarcodeScannerOptions  build =com.google.mlkit.vision.barcode.BarcodeScannerOptions.Builder  setBarcodeFormats =com.google.mlkit.vision.barcode.BarcodeScannerOptions.Builder  	getClient /com.google.mlkit.vision.barcode.BarcodeScanning  Barcode &com.google.mlkit.vision.barcode.common  FORMAT_AZTEC .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_CODE_128 .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_CODE_39 .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_DATA_MATRIX .com.google.mlkit.vision.barcode.common.Barcode  
FORMAT_EAN_13 .com.google.mlkit.vision.barcode.common.Barcode  
FORMAT_PDF417 .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_QR_CODE .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_UPC_A .com.google.mlkit.vision.barcode.common.Barcode  boundingBox .com.google.mlkit.vision.barcode.common.Barcode  cornerPoints .com.google.mlkit.vision.barcode.common.Barcode  format .com.google.mlkit.vision.barcode.common.Barcode  getBOUNDINGBox .com.google.mlkit.vision.barcode.common.Barcode  getBoundingBox .com.google.mlkit.vision.barcode.common.Barcode  getCORNERPoints .com.google.mlkit.vision.barcode.common.Barcode  getCornerPoints .com.google.mlkit.vision.barcode.common.Barcode  	getFORMAT .com.google.mlkit.vision.barcode.common.Barcode  	getFormat .com.google.mlkit.vision.barcode.common.Barcode  getRAWValue .com.google.mlkit.vision.barcode.common.Barcode  getRawValue .com.google.mlkit.vision.barcode.common.Barcode  rawValue .com.google.mlkit.vision.barcode.common.Barcode  setBoundingBox .com.google.mlkit.vision.barcode.common.Barcode  setCornerPoints .com.google.mlkit.vision.barcode.common.Barcode  	setFormat .com.google.mlkit.vision.barcode.common.Barcode  setRawValue .com.google.mlkit.vision.barcode.common.Barcode  
InputImage com.google.mlkit.vision.common  
fromBitmap )com.google.mlkit.vision.common.InputImage  BASE64_PATTERN com.olivery.BarcodeScanner  Barcode com.olivery.BarcodeScanner  BarcodeScannerOptions com.olivery.BarcodeScanner  BarcodeScannerPlugin com.olivery.BarcodeScanner  BarcodeScanning com.olivery.BarcodeScanner  Base64 com.olivery.BarcodeScanner  Bitmap com.olivery.BarcodeScanner  
BitmapFactory com.olivery.BarcodeScanner  Boolean com.olivery.BarcodeScanner  Canvas com.olivery.BarcodeScanner  Color com.olivery.BarcodeScanner  ColorMatrix com.olivery.BarcodeScanner  ColorMatrixColorFilter com.olivery.BarcodeScanner  ERROR_INVALID_INPUT com.olivery.BarcodeScanner  ERROR_NO_BARCODE_FOUND com.olivery.BarcodeScanner  ERROR_PLATFORM_ERROR com.olivery.BarcodeScanner  ERROR_UNSUPPORTED_FORMAT com.olivery.BarcodeScanner  	Exception com.olivery.BarcodeScanner  Float com.olivery.BarcodeScanner  IllegalArgumentException com.olivery.BarcodeScanner  
InputImage com.olivery.BarcodeScanner  Int com.olivery.BarcodeScanner  IntArray com.olivery.BarcodeScanner  JSObject com.olivery.BarcodeScanner  List com.olivery.BarcodeScanner  MAX_IMAGE_SIZE_BYTES com.olivery.BarcodeScanner  MEDIUM_CONTRAST_LEVEL com.olivery.BarcodeScanner  MIN_IMAGE_SIZE com.olivery.BarcodeScanner  Matrix com.olivery.BarcodeScanner  OutOfMemoryError com.olivery.BarcodeScanner  Paint com.olivery.BarcodeScanner  Pair com.olivery.BarcodeScanner  Pattern com.olivery.BarcodeScanner  String com.olivery.BarcodeScanner  android com.olivery.BarcodeScanner  apply com.olivery.BarcodeScanner  arrayOf com.olivery.BarcodeScanner  coerceIn com.olivery.BarcodeScanner  contains com.olivery.BarcodeScanner  floatArrayOf com.olivery.BarcodeScanner  forEach com.olivery.BarcodeScanner  indexOf com.olivery.BarcodeScanner  indices com.olivery.BarcodeScanner  
intArrayOf com.olivery.BarcodeScanner  isEmpty com.olivery.BarcodeScanner  
isInitialized com.olivery.BarcodeScanner  
isNotEmpty com.olivery.BarcodeScanner  
isNullOrEmpty com.olivery.BarcodeScanner  let com.olivery.BarcodeScanner  	lowercase com.olivery.BarcodeScanner  mapOf com.olivery.BarcodeScanner  max com.olivery.BarcodeScanner  min com.olivery.BarcodeScanner  minByOrNull com.olivery.BarcodeScanner  
mutableListOf com.olivery.BarcodeScanner  
plusAssign com.olivery.BarcodeScanner  split com.olivery.BarcodeScanner  
startsWith com.olivery.BarcodeScanner  	substring com.olivery.BarcodeScanner  to com.olivery.BarcodeScanner  
toMutableList com.olivery.BarcodeScanner  until com.olivery.BarcodeScanner  BASE64_PATTERN /com.olivery.BarcodeScanner.BarcodeScannerPlugin  Barcode /com.olivery.BarcodeScanner.BarcodeScannerPlugin  BarcodeScanner /com.olivery.BarcodeScanner.BarcodeScannerPlugin  BarcodeScannerOptions /com.olivery.BarcodeScanner.BarcodeScannerPlugin  BarcodeScanning /com.olivery.BarcodeScanner.BarcodeScannerPlugin  Base64 /com.olivery.BarcodeScanner.BarcodeScannerPlugin  Bitmap /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
BitmapFactory /com.olivery.BarcodeScanner.BarcodeScannerPlugin  Boolean /com.olivery.BarcodeScanner.BarcodeScannerPlugin  Canvas /com.olivery.BarcodeScanner.BarcodeScannerPlugin  Color /com.olivery.BarcodeScanner.BarcodeScannerPlugin  ColorMatrix /com.olivery.BarcodeScanner.BarcodeScannerPlugin  ColorMatrixColorFilter /com.olivery.BarcodeScanner.BarcodeScannerPlugin  ERROR_INVALID_INPUT /com.olivery.BarcodeScanner.BarcodeScannerPlugin  ERROR_NO_BARCODE_FOUND /com.olivery.BarcodeScanner.BarcodeScannerPlugin  ERROR_PLATFORM_ERROR /com.olivery.BarcodeScanner.BarcodeScannerPlugin  ERROR_UNSUPPORTED_FORMAT /com.olivery.BarcodeScanner.BarcodeScannerPlugin  	Exception /com.olivery.BarcodeScanner.BarcodeScannerPlugin  Float /com.olivery.BarcodeScanner.BarcodeScannerPlugin  IllegalArgumentException /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
InputImage /com.olivery.BarcodeScanner.BarcodeScannerPlugin  Int /com.olivery.BarcodeScanner.BarcodeScannerPlugin  IntArray /com.olivery.BarcodeScanner.BarcodeScannerPlugin  JSObject /com.olivery.BarcodeScanner.BarcodeScannerPlugin  List /com.olivery.BarcodeScanner.BarcodeScannerPlugin  MAX_IMAGE_SIZE_BYTES /com.olivery.BarcodeScanner.BarcodeScannerPlugin  MEDIUM_CONTRAST_LEVEL /com.olivery.BarcodeScanner.BarcodeScannerPlugin  MIN_IMAGE_SIZE /com.olivery.BarcodeScanner.BarcodeScannerPlugin  Matrix /com.olivery.BarcodeScanner.BarcodeScannerPlugin  OutOfMemoryError /com.olivery.BarcodeScanner.BarcodeScannerPlugin  Paint /com.olivery.BarcodeScanner.BarcodeScannerPlugin  Pair /com.olivery.BarcodeScanner.BarcodeScannerPlugin  Pattern /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
PluginCall /com.olivery.BarcodeScanner.BarcodeScannerPlugin  PluginMethod /com.olivery.BarcodeScanner.BarcodeScannerPlugin  String /com.olivery.BarcodeScanner.BarcodeScannerPlugin  android /com.olivery.BarcodeScanner.BarcodeScannerPlugin  apply /com.olivery.BarcodeScanner.BarcodeScannerPlugin  arrayOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  barcodeScanner /com.olivery.BarcodeScanner.BarcodeScannerPlugin  base64ToBitmap /com.olivery.BarcodeScanner.BarcodeScannerPlugin  cleanupBitmaps /com.olivery.BarcodeScanner.BarcodeScannerPlugin  coerceIn /com.olivery.BarcodeScanner.BarcodeScannerPlugin  contains /com.olivery.BarcodeScanner.BarcodeScannerPlugin  createBarcodeResult /com.olivery.BarcodeScanner.BarcodeScannerPlugin  createBlackWhite /com.olivery.BarcodeScanner.BarcodeScannerPlugin  createSharpened /com.olivery.BarcodeScanner.BarcodeScannerPlugin  createSimpleGrayscale /com.olivery.BarcodeScanner.BarcodeScannerPlugin  floatArrayOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getANDROID /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getAPPLY /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getARRAYOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getAndroid /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getApply /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getArrayOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getCOERCEIn /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getCONTAINS /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getCoerceIn /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getContains /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getFLOATArrayOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getFloatArrayOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getINDEXOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getINTArrayOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getISEmpty /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getISNotEmpty /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getISNullOrEmpty /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getIndexOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getIntArrayOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getIsEmpty /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getIsNotEmpty /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getIsNullOrEmpty /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getLET /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getLOWERCASE /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getLet /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getLowercase /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getMAPOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getMAX /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getMIN /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getMINByOrNull /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getMUTABLEListOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getMapOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getMax /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getMin /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getMinByOrNull /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getMutableListOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getPLUSAssign /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getPlusAssign /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getSPLIT /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getSTARTSWith /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getSUBSTRING /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getSplit /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getStartsWith /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getSubstring /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getTO /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getTOMutableList /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getTo /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getToMutableList /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getUNTIL /com.olivery.BarcodeScanner.BarcodeScannerPlugin  getUntil /com.olivery.BarcodeScanner.BarcodeScannerPlugin  indexOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  indices /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
intArrayOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  isEmpty /com.olivery.BarcodeScanner.BarcodeScannerPlugin  isHighConfidenceImage /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
isInitialized /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
isNotEmpty /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
isNullOrEmpty /com.olivery.BarcodeScanner.BarcodeScannerPlugin  let /com.olivery.BarcodeScanner.BarcodeScannerPlugin  	lowercase /com.olivery.BarcodeScanner.BarcodeScannerPlugin  mapBarcodeFormat /com.olivery.BarcodeScanner.BarcodeScannerPlugin  mapOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  max /com.olivery.BarcodeScanner.BarcodeScannerPlugin  min /com.olivery.BarcodeScanner.BarcodeScannerPlugin  minByOrNull /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
mutableListOf /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
plusAssign /com.olivery.BarcodeScanner.BarcodeScannerPlugin  preprocessImage /com.olivery.BarcodeScanner.BarcodeScannerPlugin  rejectWithError /com.olivery.BarcodeScanner.BarcodeScannerPlugin  resizeImageIfNeeded /com.olivery.BarcodeScanner.BarcodeScannerPlugin  rotateBitmap /com.olivery.BarcodeScanner.BarcodeScannerPlugin  scanWithMultipleImages /com.olivery.BarcodeScanner.BarcodeScannerPlugin  selectBestBarcode /com.olivery.BarcodeScanner.BarcodeScannerPlugin  split /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
startsWith /com.olivery.BarcodeScanner.BarcodeScannerPlugin  	substring /com.olivery.BarcodeScanner.BarcodeScannerPlugin  to /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
toMutableList /com.olivery.BarcodeScanner.BarcodeScannerPlugin  until /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
validateInput /com.olivery.BarcodeScanner.BarcodeScannerPlugin  BASE64_PATTERN 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  Barcode 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  BarcodeScanner 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  BarcodeScannerOptions 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  BarcodeScanning 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  Base64 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  Bitmap 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
BitmapFactory 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  Boolean 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  Canvas 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  Color 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  ColorMatrix 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  ColorMatrixColorFilter 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  ERROR_INVALID_INPUT 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  ERROR_NO_BARCODE_FOUND 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  ERROR_PLATFORM_ERROR 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  ERROR_UNSUPPORTED_FORMAT 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  	Exception 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  Float 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  IllegalArgumentException 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
InputImage 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  Int 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  IntArray 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  JSObject 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  List 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  MAX_IMAGE_SIZE_BYTES 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  MEDIUM_CONTRAST_LEVEL 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  MIN_IMAGE_SIZE 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  Matrix 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  OutOfMemoryError 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  Paint 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  Pair 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  Pattern 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
PluginCall 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  PluginMethod 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  String 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  android 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  apply 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  arrayOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  coerceIn 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  contains 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  floatArrayOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getANDROID 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getAPPLY 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getARRAYOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getAndroid 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getApply 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getArrayOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getCOERCEIn 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getCONTAINS 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getCoerceIn 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getContains 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getFLOATArrayOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getFloatArrayOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getINDEXOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getINTArrayOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getISEmpty 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getISNotEmpty 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getISNullOrEmpty 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getIndexOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getIntArrayOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getIsEmpty 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getIsNotEmpty 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getIsNullOrEmpty 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getLET 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getLOWERCASE 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getLet 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getLowercase 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getMAPOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getMAX 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getMIN 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getMINByOrNull 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getMUTABLEListOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getMapOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getMax 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getMin 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getMinByOrNull 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getMutableListOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getPLUSAssign 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getPlusAssign 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getSPLIT 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getSTARTSWith 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getSUBSTRING 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getSplit 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getStartsWith 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getSubstring 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getTO 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getTOMutableList 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getTo 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getToMutableList 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getUNTIL 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  getUntil 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  indexOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  indices 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
intArrayOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  isEmpty 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
isInitialized 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
isNotEmpty 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
isNullOrEmpty 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  let 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  	lowercase 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  mapOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  max 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  min 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  minByOrNull 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
mutableListOf 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
plusAssign 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  split 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
startsWith 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  	substring 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  to 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
toMutableList 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  until 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  BASE64_PATTERN 	java.lang  Barcode 	java.lang  BarcodeScannerOptions 	java.lang  BarcodeScanning 	java.lang  Base64 	java.lang  Bitmap 	java.lang  
BitmapFactory 	java.lang  Canvas 	java.lang  Color 	java.lang  ColorMatrix 	java.lang  ColorMatrixColorFilter 	java.lang  ERROR_INVALID_INPUT 	java.lang  ERROR_NO_BARCODE_FOUND 	java.lang  ERROR_PLATFORM_ERROR 	java.lang  ERROR_UNSUPPORTED_FORMAT 	java.lang  	Exception 	java.lang  
InputImage 	java.lang  IntArray 	java.lang  JSObject 	java.lang  MAX_IMAGE_SIZE_BYTES 	java.lang  MEDIUM_CONTRAST_LEVEL 	java.lang  MIN_IMAGE_SIZE 	java.lang  Matrix 	java.lang  OutOfMemoryError 	java.lang  Paint 	java.lang  Pair 	java.lang  Pattern 	java.lang  android 	java.lang  apply 	java.lang  arrayOf 	java.lang  coerceIn 	java.lang  contains 	java.lang  floatArrayOf 	java.lang  forEach 	java.lang  indexOf 	java.lang  indices 	java.lang  
intArrayOf 	java.lang  isEmpty 	java.lang  
isInitialized 	java.lang  
isNotEmpty 	java.lang  
isNullOrEmpty 	java.lang  let 	java.lang  	lowercase 	java.lang  mapOf 	java.lang  max 	java.lang  min 	java.lang  minByOrNull 	java.lang  
mutableListOf 	java.lang  
plusAssign 	java.lang  split 	java.lang  
startsWith 	java.lang  	substring 	java.lang  to 	java.lang  
toMutableList 	java.lang  until 	java.lang  message java.lang.Exception  message "java.lang.IllegalArgumentException  Pattern java.util.regex  matches java.util.regex.Matcher  compile java.util.regex.Pattern  matcher java.util.regex.Pattern  Any kotlin  Array kotlin  BASE64_PATTERN kotlin  Barcode kotlin  BarcodeScannerOptions kotlin  BarcodeScanning kotlin  Base64 kotlin  Bitmap kotlin  
BitmapFactory kotlin  Boolean kotlin  	ByteArray kotlin  Canvas kotlin  Color kotlin  ColorMatrix kotlin  ColorMatrixColorFilter kotlin  Double kotlin  ERROR_INVALID_INPUT kotlin  ERROR_NO_BARCODE_FOUND kotlin  ERROR_PLATFORM_ERROR kotlin  ERROR_UNSUPPORTED_FORMAT kotlin  	Exception kotlin  Float kotlin  
FloatArray kotlin  	Function1 kotlin  IllegalArgumentException kotlin  
InputImage kotlin  Int kotlin  IntArray kotlin  JSObject kotlin  MAX_IMAGE_SIZE_BYTES kotlin  MEDIUM_CONTRAST_LEVEL kotlin  MIN_IMAGE_SIZE kotlin  Matrix kotlin  Nothing kotlin  OutOfMemoryError kotlin  Paint kotlin  Pair kotlin  Pattern kotlin  String kotlin  Unit kotlin  android kotlin  apply kotlin  arrayOf kotlin  coerceIn kotlin  contains kotlin  floatArrayOf kotlin  forEach kotlin  indexOf kotlin  indices kotlin  
intArrayOf kotlin  isEmpty kotlin  
isInitialized kotlin  
isNotEmpty kotlin  
isNullOrEmpty kotlin  let kotlin  	lowercase kotlin  mapOf kotlin  max kotlin  min kotlin  minByOrNull kotlin  
mutableListOf kotlin  
plusAssign kotlin  split kotlin  
startsWith kotlin  	substring kotlin  to kotlin  
toMutableList kotlin  until kotlin  getLET kotlin.Array  getLet kotlin.Array  
getISEmpty kotlin.ByteArray  
getIsEmpty kotlin.ByteArray  isEmpty kotlin.ByteArray  getCOERCEIn 
kotlin.Int  getCoerceIn 
kotlin.Int  
getPLUSAssign 
kotlin.Int  
getPlusAssign 
kotlin.Int  getTO 
kotlin.Int  getTo 
kotlin.Int  getUNTIL 
kotlin.Int  getUntil 
kotlin.Int  
getINDICES kotlin.IntArray  
getIndices kotlin.IntArray  equals kotlin.Pair  first kotlin.Pair  second kotlin.Pair  getCONTAINS 
kotlin.String  getContains 
kotlin.String  
getINDEXOf 
kotlin.String  
getISEmpty 
kotlin.String  
getISNotEmpty 
kotlin.String  getISNullOrEmpty 
kotlin.String  
getIndexOf 
kotlin.String  
getIsEmpty 
kotlin.String  
getIsNotEmpty 
kotlin.String  getIsNullOrEmpty 
kotlin.String  getLOWERCASE 
kotlin.String  getLowercase 
kotlin.String  getSPLIT 
kotlin.String  
getSTARTSWith 
kotlin.String  getSUBSTRING 
kotlin.String  getSplit 
kotlin.String  
getStartsWith 
kotlin.String  getSubstring 
kotlin.String  isEmpty 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrEmpty 
kotlin.String  BASE64_PATTERN kotlin.annotation  Barcode kotlin.annotation  BarcodeScannerOptions kotlin.annotation  BarcodeScanning kotlin.annotation  Base64 kotlin.annotation  Bitmap kotlin.annotation  
BitmapFactory kotlin.annotation  Canvas kotlin.annotation  Color kotlin.annotation  ColorMatrix kotlin.annotation  ColorMatrixColorFilter kotlin.annotation  ERROR_INVALID_INPUT kotlin.annotation  ERROR_NO_BARCODE_FOUND kotlin.annotation  ERROR_PLATFORM_ERROR kotlin.annotation  ERROR_UNSUPPORTED_FORMAT kotlin.annotation  	Exception kotlin.annotation  IllegalArgumentException kotlin.annotation  
InputImage kotlin.annotation  IntArray kotlin.annotation  JSObject kotlin.annotation  MAX_IMAGE_SIZE_BYTES kotlin.annotation  MEDIUM_CONTRAST_LEVEL kotlin.annotation  MIN_IMAGE_SIZE kotlin.annotation  Matrix kotlin.annotation  OutOfMemoryError kotlin.annotation  Paint kotlin.annotation  Pair kotlin.annotation  Pattern kotlin.annotation  android kotlin.annotation  apply kotlin.annotation  arrayOf kotlin.annotation  coerceIn kotlin.annotation  contains kotlin.annotation  floatArrayOf kotlin.annotation  forEach kotlin.annotation  indexOf kotlin.annotation  indices kotlin.annotation  
intArrayOf kotlin.annotation  isEmpty kotlin.annotation  
isInitialized kotlin.annotation  
isNotEmpty kotlin.annotation  
isNullOrEmpty kotlin.annotation  let kotlin.annotation  	lowercase kotlin.annotation  mapOf kotlin.annotation  max kotlin.annotation  min kotlin.annotation  minByOrNull kotlin.annotation  
mutableListOf kotlin.annotation  
plusAssign kotlin.annotation  split kotlin.annotation  
startsWith kotlin.annotation  	substring kotlin.annotation  to kotlin.annotation  
toMutableList kotlin.annotation  until kotlin.annotation  BASE64_PATTERN kotlin.collections  Barcode kotlin.collections  BarcodeScannerOptions kotlin.collections  BarcodeScanning kotlin.collections  Base64 kotlin.collections  Bitmap kotlin.collections  
BitmapFactory kotlin.collections  Canvas kotlin.collections  Color kotlin.collections  ColorMatrix kotlin.collections  ColorMatrixColorFilter kotlin.collections  ERROR_INVALID_INPUT kotlin.collections  ERROR_NO_BARCODE_FOUND kotlin.collections  ERROR_PLATFORM_ERROR kotlin.collections  ERROR_UNSUPPORTED_FORMAT kotlin.collections  	Exception kotlin.collections  IllegalArgumentException kotlin.collections  
InputImage kotlin.collections  IntArray kotlin.collections  JSObject kotlin.collections  List kotlin.collections  MAX_IMAGE_SIZE_BYTES kotlin.collections  MEDIUM_CONTRAST_LEVEL kotlin.collections  MIN_IMAGE_SIZE kotlin.collections  Map kotlin.collections  Matrix kotlin.collections  MutableList kotlin.collections  OutOfMemoryError kotlin.collections  Paint kotlin.collections  Pair kotlin.collections  Pattern kotlin.collections  android kotlin.collections  apply kotlin.collections  arrayOf kotlin.collections  coerceIn kotlin.collections  contains kotlin.collections  floatArrayOf kotlin.collections  forEach kotlin.collections  indexOf kotlin.collections  indices kotlin.collections  
intArrayOf kotlin.collections  isEmpty kotlin.collections  
isInitialized kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  let kotlin.collections  	lowercase kotlin.collections  mapOf kotlin.collections  max kotlin.collections  min kotlin.collections  minByOrNull kotlin.collections  
mutableListOf kotlin.collections  
plusAssign kotlin.collections  split kotlin.collections  
startsWith kotlin.collections  	substring kotlin.collections  to kotlin.collections  
toMutableList kotlin.collections  until kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  getMINByOrNull kotlin.collections.List  getMinByOrNull kotlin.collections.List  getTOMutableList kotlin.collections.List  getToMutableList kotlin.collections.List  
getISNotEmpty kotlin.collections.MutableList  
getIsNotEmpty kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  BASE64_PATTERN kotlin.comparisons  Barcode kotlin.comparisons  BarcodeScannerOptions kotlin.comparisons  BarcodeScanning kotlin.comparisons  Base64 kotlin.comparisons  Bitmap kotlin.comparisons  
BitmapFactory kotlin.comparisons  Canvas kotlin.comparisons  Color kotlin.comparisons  ColorMatrix kotlin.comparisons  ColorMatrixColorFilter kotlin.comparisons  ERROR_INVALID_INPUT kotlin.comparisons  ERROR_NO_BARCODE_FOUND kotlin.comparisons  ERROR_PLATFORM_ERROR kotlin.comparisons  ERROR_UNSUPPORTED_FORMAT kotlin.comparisons  	Exception kotlin.comparisons  IllegalArgumentException kotlin.comparisons  
InputImage kotlin.comparisons  IntArray kotlin.comparisons  JSObject kotlin.comparisons  MAX_IMAGE_SIZE_BYTES kotlin.comparisons  MEDIUM_CONTRAST_LEVEL kotlin.comparisons  MIN_IMAGE_SIZE kotlin.comparisons  Matrix kotlin.comparisons  OutOfMemoryError kotlin.comparisons  Paint kotlin.comparisons  Pair kotlin.comparisons  Pattern kotlin.comparisons  android kotlin.comparisons  apply kotlin.comparisons  arrayOf kotlin.comparisons  coerceIn kotlin.comparisons  contains kotlin.comparisons  floatArrayOf kotlin.comparisons  forEach kotlin.comparisons  indexOf kotlin.comparisons  indices kotlin.comparisons  
intArrayOf kotlin.comparisons  isEmpty kotlin.comparisons  
isInitialized kotlin.comparisons  
isNotEmpty kotlin.comparisons  
isNullOrEmpty kotlin.comparisons  let kotlin.comparisons  	lowercase kotlin.comparisons  mapOf kotlin.comparisons  max kotlin.comparisons  min kotlin.comparisons  minByOrNull kotlin.comparisons  
mutableListOf kotlin.comparisons  
plusAssign kotlin.comparisons  split kotlin.comparisons  
startsWith kotlin.comparisons  	substring kotlin.comparisons  to kotlin.comparisons  
toMutableList kotlin.comparisons  until kotlin.comparisons  BASE64_PATTERN 	kotlin.io  Barcode 	kotlin.io  BarcodeScannerOptions 	kotlin.io  BarcodeScanning 	kotlin.io  Base64 	kotlin.io  Bitmap 	kotlin.io  
BitmapFactory 	kotlin.io  Canvas 	kotlin.io  Color 	kotlin.io  ColorMatrix 	kotlin.io  ColorMatrixColorFilter 	kotlin.io  ERROR_INVALID_INPUT 	kotlin.io  ERROR_NO_BARCODE_FOUND 	kotlin.io  ERROR_PLATFORM_ERROR 	kotlin.io  ERROR_UNSUPPORTED_FORMAT 	kotlin.io  	Exception 	kotlin.io  IllegalArgumentException 	kotlin.io  
InputImage 	kotlin.io  IntArray 	kotlin.io  JSObject 	kotlin.io  MAX_IMAGE_SIZE_BYTES 	kotlin.io  MEDIUM_CONTRAST_LEVEL 	kotlin.io  MIN_IMAGE_SIZE 	kotlin.io  Matrix 	kotlin.io  OutOfMemoryError 	kotlin.io  Paint 	kotlin.io  Pair 	kotlin.io  Pattern 	kotlin.io  android 	kotlin.io  apply 	kotlin.io  arrayOf 	kotlin.io  coerceIn 	kotlin.io  contains 	kotlin.io  floatArrayOf 	kotlin.io  forEach 	kotlin.io  indexOf 	kotlin.io  indices 	kotlin.io  
intArrayOf 	kotlin.io  isEmpty 	kotlin.io  
isInitialized 	kotlin.io  
isNotEmpty 	kotlin.io  
isNullOrEmpty 	kotlin.io  let 	kotlin.io  	lowercase 	kotlin.io  mapOf 	kotlin.io  max 	kotlin.io  min 	kotlin.io  minByOrNull 	kotlin.io  
mutableListOf 	kotlin.io  
plusAssign 	kotlin.io  split 	kotlin.io  
startsWith 	kotlin.io  	substring 	kotlin.io  to 	kotlin.io  
toMutableList 	kotlin.io  until 	kotlin.io  BASE64_PATTERN 
kotlin.jvm  Barcode 
kotlin.jvm  BarcodeScannerOptions 
kotlin.jvm  BarcodeScanning 
kotlin.jvm  Base64 
kotlin.jvm  Bitmap 
kotlin.jvm  
BitmapFactory 
kotlin.jvm  Canvas 
kotlin.jvm  Color 
kotlin.jvm  ColorMatrix 
kotlin.jvm  ColorMatrixColorFilter 
kotlin.jvm  ERROR_INVALID_INPUT 
kotlin.jvm  ERROR_NO_BARCODE_FOUND 
kotlin.jvm  ERROR_PLATFORM_ERROR 
kotlin.jvm  ERROR_UNSUPPORTED_FORMAT 
kotlin.jvm  	Exception 
kotlin.jvm  IllegalArgumentException 
kotlin.jvm  
InputImage 
kotlin.jvm  IntArray 
kotlin.jvm  JSObject 
kotlin.jvm  MAX_IMAGE_SIZE_BYTES 
kotlin.jvm  MEDIUM_CONTRAST_LEVEL 
kotlin.jvm  MIN_IMAGE_SIZE 
kotlin.jvm  Matrix 
kotlin.jvm  OutOfMemoryError 
kotlin.jvm  Paint 
kotlin.jvm  Pair 
kotlin.jvm  Pattern 
kotlin.jvm  android 
kotlin.jvm  apply 
kotlin.jvm  arrayOf 
kotlin.jvm  coerceIn 
kotlin.jvm  contains 
kotlin.jvm  floatArrayOf 
kotlin.jvm  forEach 
kotlin.jvm  indexOf 
kotlin.jvm  indices 
kotlin.jvm  
intArrayOf 
kotlin.jvm  isEmpty 
kotlin.jvm  
isInitialized 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  
isNullOrEmpty 
kotlin.jvm  let 
kotlin.jvm  	lowercase 
kotlin.jvm  mapOf 
kotlin.jvm  max 
kotlin.jvm  min 
kotlin.jvm  minByOrNull 
kotlin.jvm  
mutableListOf 
kotlin.jvm  
plusAssign 
kotlin.jvm  split 
kotlin.jvm  
startsWith 
kotlin.jvm  	substring 
kotlin.jvm  to 
kotlin.jvm  
toMutableList 
kotlin.jvm  until 
kotlin.jvm  PI kotlin.math  abs kotlin.math  cos kotlin.math  max kotlin.math  min kotlin.math  pow kotlin.math  sin kotlin.math  sqrt kotlin.math  BASE64_PATTERN 
kotlin.ranges  Barcode 
kotlin.ranges  BarcodeScannerOptions 
kotlin.ranges  BarcodeScanning 
kotlin.ranges  Base64 
kotlin.ranges  Bitmap 
kotlin.ranges  
BitmapFactory 
kotlin.ranges  Canvas 
kotlin.ranges  Color 
kotlin.ranges  ColorMatrix 
kotlin.ranges  ColorMatrixColorFilter 
kotlin.ranges  ERROR_INVALID_INPUT 
kotlin.ranges  ERROR_NO_BARCODE_FOUND 
kotlin.ranges  ERROR_PLATFORM_ERROR 
kotlin.ranges  ERROR_UNSUPPORTED_FORMAT 
kotlin.ranges  	Exception 
kotlin.ranges  IllegalArgumentException 
kotlin.ranges  
InputImage 
kotlin.ranges  IntArray 
kotlin.ranges  IntRange 
kotlin.ranges  JSObject 
kotlin.ranges  MAX_IMAGE_SIZE_BYTES 
kotlin.ranges  MEDIUM_CONTRAST_LEVEL 
kotlin.ranges  MIN_IMAGE_SIZE 
kotlin.ranges  Matrix 
kotlin.ranges  OutOfMemoryError 
kotlin.ranges  Paint 
kotlin.ranges  Pair 
kotlin.ranges  Pattern 
kotlin.ranges  android 
kotlin.ranges  apply 
kotlin.ranges  arrayOf 
kotlin.ranges  coerceIn 
kotlin.ranges  contains 
kotlin.ranges  floatArrayOf 
kotlin.ranges  forEach 
kotlin.ranges  indexOf 
kotlin.ranges  indices 
kotlin.ranges  
intArrayOf 
kotlin.ranges  isEmpty 
kotlin.ranges  
isInitialized 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  
isNullOrEmpty 
kotlin.ranges  let 
kotlin.ranges  	lowercase 
kotlin.ranges  mapOf 
kotlin.ranges  max 
kotlin.ranges  min 
kotlin.ranges  minByOrNull 
kotlin.ranges  
mutableListOf 
kotlin.ranges  
plusAssign 
kotlin.ranges  split 
kotlin.ranges  
startsWith 
kotlin.ranges  	substring 
kotlin.ranges  to 
kotlin.ranges  
toMutableList 
kotlin.ranges  until 
kotlin.ranges  contains kotlin.ranges.IntProgression  iterator kotlin.ranges.IntProgression  contains kotlin.ranges.IntRange  iterator kotlin.ranges.IntRange  KMutableProperty0 kotlin.reflect  getISInitialized  kotlin.reflect.KMutableProperty0  getIsInitialized  kotlin.reflect.KMutableProperty0  
isInitialized  kotlin.reflect.KMutableProperty0  BASE64_PATTERN kotlin.sequences  Barcode kotlin.sequences  BarcodeScannerOptions kotlin.sequences  BarcodeScanning kotlin.sequences  Base64 kotlin.sequences  Bitmap kotlin.sequences  
BitmapFactory kotlin.sequences  Canvas kotlin.sequences  Color kotlin.sequences  ColorMatrix kotlin.sequences  ColorMatrixColorFilter kotlin.sequences  ERROR_INVALID_INPUT kotlin.sequences  ERROR_NO_BARCODE_FOUND kotlin.sequences  ERROR_PLATFORM_ERROR kotlin.sequences  ERROR_UNSUPPORTED_FORMAT kotlin.sequences  	Exception kotlin.sequences  IllegalArgumentException kotlin.sequences  
InputImage kotlin.sequences  IntArray kotlin.sequences  JSObject kotlin.sequences  MAX_IMAGE_SIZE_BYTES kotlin.sequences  MEDIUM_CONTRAST_LEVEL kotlin.sequences  MIN_IMAGE_SIZE kotlin.sequences  Matrix kotlin.sequences  OutOfMemoryError kotlin.sequences  Paint kotlin.sequences  Pair kotlin.sequences  Pattern kotlin.sequences  android kotlin.sequences  apply kotlin.sequences  arrayOf kotlin.sequences  coerceIn kotlin.sequences  contains kotlin.sequences  floatArrayOf kotlin.sequences  forEach kotlin.sequences  indexOf kotlin.sequences  indices kotlin.sequences  
intArrayOf kotlin.sequences  isEmpty kotlin.sequences  
isInitialized kotlin.sequences  
isNotEmpty kotlin.sequences  
isNullOrEmpty kotlin.sequences  let kotlin.sequences  	lowercase kotlin.sequences  mapOf kotlin.sequences  max kotlin.sequences  min kotlin.sequences  minByOrNull kotlin.sequences  
mutableListOf kotlin.sequences  
plusAssign kotlin.sequences  split kotlin.sequences  
startsWith kotlin.sequences  	substring kotlin.sequences  to kotlin.sequences  
toMutableList kotlin.sequences  until kotlin.sequences  BASE64_PATTERN kotlin.text  Barcode kotlin.text  BarcodeScannerOptions kotlin.text  BarcodeScanning kotlin.text  Base64 kotlin.text  Bitmap kotlin.text  
BitmapFactory kotlin.text  Canvas kotlin.text  Color kotlin.text  ColorMatrix kotlin.text  ColorMatrixColorFilter kotlin.text  ERROR_INVALID_INPUT kotlin.text  ERROR_NO_BARCODE_FOUND kotlin.text  ERROR_PLATFORM_ERROR kotlin.text  ERROR_UNSUPPORTED_FORMAT kotlin.text  	Exception kotlin.text  IllegalArgumentException kotlin.text  
InputImage kotlin.text  IntArray kotlin.text  JSObject kotlin.text  MAX_IMAGE_SIZE_BYTES kotlin.text  MEDIUM_CONTRAST_LEVEL kotlin.text  MIN_IMAGE_SIZE kotlin.text  Matrix kotlin.text  OutOfMemoryError kotlin.text  Paint kotlin.text  Pair kotlin.text  Pattern kotlin.text  android kotlin.text  apply kotlin.text  arrayOf kotlin.text  coerceIn kotlin.text  contains kotlin.text  floatArrayOf kotlin.text  forEach kotlin.text  indexOf kotlin.text  indices kotlin.text  
intArrayOf kotlin.text  isEmpty kotlin.text  
isInitialized kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  let kotlin.text  	lowercase kotlin.text  mapOf kotlin.text  max kotlin.text  min kotlin.text  minByOrNull kotlin.text  
mutableListOf kotlin.text  
plusAssign kotlin.text  split kotlin.text  
startsWith kotlin.text  	substring kotlin.text  to kotlin.text  
toMutableList kotlin.text  until kotlin.text  put org.json.JSONObject  validateBarcodeSize /com.olivery.BarcodeScanner.BarcodeScannerPlugin  ERROR_BARCODE_TOO_SMALL 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  ERROR_BARCODE_TOO_SMALL /com.olivery.BarcodeScanner.BarcodeScannerPlugin  d android.util.Log  ERROR_BARCODE_TOO_SMALL com.getcapacitor.Plugin  MIN_BARCODE_SIZE_PERCENTAGE com.getcapacitor.Plugin  format com.getcapacitor.Plugin  validateBarcodeSize com.getcapacitor.Plugin  ERROR_BARCODE_TOO_SMALL com.olivery.BarcodeScanner  MIN_BARCODE_SIZE_PERCENTAGE com.olivery.BarcodeScanner  format com.olivery.BarcodeScanner  MIN_BARCODE_SIZE_PERCENTAGE /com.olivery.BarcodeScanner.BarcodeScannerPlugin  format /com.olivery.BarcodeScanner.BarcodeScannerPlugin  	getFORMAT /com.olivery.BarcodeScanner.BarcodeScannerPlugin  	getFormat /com.olivery.BarcodeScanner.BarcodeScannerPlugin  MIN_BARCODE_SIZE_PERCENTAGE 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  format 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  	getFORMAT 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  	getFormat 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  ERROR_BARCODE_TOO_SMALL 	java.lang  MIN_BARCODE_SIZE_PERCENTAGE 	java.lang  String 	java.lang  format 	java.lang  ERROR_BARCODE_TOO_SMALL kotlin  MIN_BARCODE_SIZE_PERCENTAGE kotlin  format kotlin  	getFORMAT kotlin.String.Companion  	getFormat kotlin.String.Companion  ERROR_BARCODE_TOO_SMALL kotlin.annotation  MIN_BARCODE_SIZE_PERCENTAGE kotlin.annotation  String kotlin.annotation  format kotlin.annotation  ERROR_BARCODE_TOO_SMALL kotlin.collections  MIN_BARCODE_SIZE_PERCENTAGE kotlin.collections  String kotlin.collections  format kotlin.collections  ERROR_BARCODE_TOO_SMALL kotlin.comparisons  MIN_BARCODE_SIZE_PERCENTAGE kotlin.comparisons  String kotlin.comparisons  format kotlin.comparisons  ERROR_BARCODE_TOO_SMALL 	kotlin.io  MIN_BARCODE_SIZE_PERCENTAGE 	kotlin.io  String 	kotlin.io  format 	kotlin.io  ERROR_BARCODE_TOO_SMALL 
kotlin.jvm  MIN_BARCODE_SIZE_PERCENTAGE 
kotlin.jvm  String 
kotlin.jvm  format 
kotlin.jvm  ERROR_BARCODE_TOO_SMALL 
kotlin.ranges  MIN_BARCODE_SIZE_PERCENTAGE 
kotlin.ranges  String 
kotlin.ranges  format 
kotlin.ranges  ERROR_BARCODE_TOO_SMALL kotlin.sequences  MIN_BARCODE_SIZE_PERCENTAGE kotlin.sequences  String kotlin.sequences  format kotlin.sequences  ERROR_BARCODE_TOO_SMALL kotlin.text  MIN_BARCODE_SIZE_PERCENTAGE kotlin.text  String kotlin.text  format kotlin.text  
component1 kotlin.Pair  
component2 kotlin.Pair  
postConcat android.graphics.ColorMatrix  Regex com.getcapacitor.Plugin  correctBarcodeValue com.getcapacitor.Plugin  correctCommonMisreading com.getcapacitor.Plugin  correctZeroMisreading com.getcapacitor.Plugin  createCharacterEnhanced com.getcapacitor.Plugin  invoke com.getcapacitor.Plugin  matches com.getcapacitor.Plugin  replace com.getcapacitor.Plugin  Regex com.olivery.BarcodeScanner  invoke com.olivery.BarcodeScanner  matches com.olivery.BarcodeScanner  replace com.olivery.BarcodeScanner  Regex /com.olivery.BarcodeScanner.BarcodeScannerPlugin  correctBarcodeValue /com.olivery.BarcodeScanner.BarcodeScannerPlugin  correctCommonMisreading /com.olivery.BarcodeScanner.BarcodeScannerPlugin  correctZeroMisreading /com.olivery.BarcodeScanner.BarcodeScannerPlugin  createCharacterEnhanced /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getMATCHES /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getMatches /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getREPLACE /com.olivery.BarcodeScanner.BarcodeScannerPlugin  
getReplace /com.olivery.BarcodeScanner.BarcodeScannerPlugin  invoke /com.olivery.BarcodeScanner.BarcodeScannerPlugin  matches /com.olivery.BarcodeScanner.BarcodeScannerPlugin  replace /com.olivery.BarcodeScanner.BarcodeScannerPlugin  Regex 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getMATCHES 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getMatches 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getREPLACE 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  
getReplace 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  invoke 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  matches 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  replace 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  Regex 	java.lang  invoke 	java.lang  matches 	java.lang  replace 	java.lang  Char kotlin  Regex kotlin  invoke kotlin  matches kotlin  replace kotlin  
getMATCHES 
kotlin.String  
getMatches 
kotlin.String  
getREPLACE 
kotlin.String  
getReplace 
kotlin.String  Regex kotlin.annotation  invoke kotlin.annotation  matches kotlin.annotation  replace kotlin.annotation  Regex kotlin.collections  invoke kotlin.collections  matches kotlin.collections  replace kotlin.collections  Regex kotlin.comparisons  invoke kotlin.comparisons  matches kotlin.comparisons  replace kotlin.comparisons  Regex 	kotlin.io  invoke 	kotlin.io  matches 	kotlin.io  replace 	kotlin.io  Regex 
kotlin.jvm  invoke 
kotlin.jvm  matches 
kotlin.jvm  replace 
kotlin.jvm  Regex 
kotlin.ranges  invoke 
kotlin.ranges  matches 
kotlin.ranges  replace 
kotlin.ranges  Regex kotlin.sequences  invoke kotlin.sequences  matches kotlin.sequences  replace kotlin.sequences  Regex kotlin.text  invoke kotlin.text  matches kotlin.text  replace kotlin.text  invoke kotlin.text.Regex.Companion  HIGH_CONTRAST_LEVEL com.getcapacitor.Plugin  ULTRA_HIGH_CONTRAST_LEVEL com.getcapacitor.Plugin  createContrastEnhanced com.getcapacitor.Plugin  HIGH_CONTRAST_LEVEL com.olivery.BarcodeScanner  ULTRA_HIGH_CONTRAST_LEVEL com.olivery.BarcodeScanner  HIGH_CONTRAST_LEVEL /com.olivery.BarcodeScanner.BarcodeScannerPlugin  ULTRA_HIGH_CONTRAST_LEVEL /com.olivery.BarcodeScanner.BarcodeScannerPlugin  createContrastEnhanced /com.olivery.BarcodeScanner.BarcodeScannerPlugin  HIGH_CONTRAST_LEVEL 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  ULTRA_HIGH_CONTRAST_LEVEL 9com.olivery.BarcodeScanner.BarcodeScannerPlugin.Companion  HIGH_CONTRAST_LEVEL 	java.lang  ULTRA_HIGH_CONTRAST_LEVEL 	java.lang  HIGH_CONTRAST_LEVEL kotlin  ULTRA_HIGH_CONTRAST_LEVEL kotlin  HIGH_CONTRAST_LEVEL kotlin.annotation  ULTRA_HIGH_CONTRAST_LEVEL kotlin.annotation  HIGH_CONTRAST_LEVEL kotlin.collections  ULTRA_HIGH_CONTRAST_LEVEL kotlin.collections  HIGH_CONTRAST_LEVEL kotlin.comparisons  ULTRA_HIGH_CONTRAST_LEVEL kotlin.comparisons  HIGH_CONTRAST_LEVEL 	kotlin.io  ULTRA_HIGH_CONTRAST_LEVEL 	kotlin.io  HIGH_CONTRAST_LEVEL 
kotlin.jvm  ULTRA_HIGH_CONTRAST_LEVEL 
kotlin.jvm  HIGH_CONTRAST_LEVEL 
kotlin.ranges  ULTRA_HIGH_CONTRAST_LEVEL 
kotlin.ranges  HIGH_CONTRAST_LEVEL kotlin.sequences  ULTRA_HIGH_CONTRAST_LEVEL kotlin.sequences  HIGH_CONTRAST_LEVEL kotlin.text  ULTRA_HIGH_CONTRAST_LEVEL kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               