<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.google.mlkit:barcode-scanning:17.2.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/8.11.1/transforms/d7d0b88d86e83297c0230d3fe32393ea/transformed/barcode-scanning-17.2.0/assets"><file name="mlkit_barcode_models/barcode_ssd_mobilenet_v1_dmp25_quant.tflite" path="/Users/<USER>/.gradle/caches/8.11.1/transforms/d7d0b88d86e83297c0230d3fe32393ea/transformed/barcode-scanning-17.2.0/assets/mlkit_barcode_models/barcode_ssd_mobilenet_v1_dmp25_quant.tflite"/><file name="mlkit_barcode_models/oned_auto_regressor_mobile.tflite" path="/Users/<USER>/.gradle/caches/8.11.1/transforms/d7d0b88d86e83297c0230d3fe32393ea/transformed/barcode-scanning-17.2.0/assets/mlkit_barcode_models/oned_auto_regressor_mobile.tflite"/><file name="mlkit_barcode_models/oned_feature_extractor_mobile.tflite" path="/Users/<USER>/.gradle/caches/8.11.1/transforms/d7d0b88d86e83297c0230d3fe32393ea/transformed/barcode-scanning-17.2.0/assets/mlkit_barcode_models/oned_feature_extractor_mobile.tflite"/></source></dataSet><dataSet config=":capacitor-android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/olivery_web/olivery_appx/node_modules/@capacitor/android/capacitor/build/intermediates/library_assets/debug/packageDebugAssets/out"><file name="native-bridge.js" path="/Users/<USER>/Desktop/olivery_web/olivery_appx/node_modules/@capacitor/android/capacitor/build/intermediates/library_assets/debug/packageDebugAssets/out/native-bridge.js"/></source></dataSet><dataSet config=":barcode-scanner" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/olivery_web/olivery_appx/barcode-scanner/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config="androidTest" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/olivery_web/olivery_appx/barcode-scanner/android/src/androidTest/assets"/></dataSet><dataSet config="androidTestDebug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/olivery_web/olivery_appx/barcode-scanner/android/src/androidTestDebug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/olivery_web/olivery_appx/barcode-scanner/android/build/intermediates/shader_assets/debugAndroidTest/compileDebugAndroidTestShaders/out"/></dataSet></merger>