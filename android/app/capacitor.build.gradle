// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_21
      targetCompatibility JavaVersion.VERSION_21
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-app')
    implementation project(':capacitor-barcode-scanner')
    implementation project(':capacitor-browser')
    implementation project(':capacitor-camera')
    implementation project(':capacitor-device')
    implementation project(':capacitor-dialog')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-geolocation')
    implementation project(':capacitor-haptics')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-network')
    implementation project(':capacitor-share')
    implementation project(':capacitor-splash-screen')
    implementation project(':capacitor-status-bar')
    implementation project(':capawesome-team-capacitor-file-opener')
    implementation project(':capgo-capacitor-updater')
    implementation project(':barcode-scanner')
    implementation project(':capacitor-native-settings')
    implementation "com.onesignal:OneSignal:5.1.17"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10"
}
apply from: "../../node_modules/onesignal-cordova-plugin/build-extras-onesignal.gradle"

if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
