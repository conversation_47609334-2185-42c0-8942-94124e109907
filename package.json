{"name": "Bo<PERSON>", "version": "7.08.145", "private": true, "description": "An Ionic project", "homepage": "https://ionicframework.com/", "author": "Ionic Framework", "scripts": {"build": "ng build", "build-android": "ionic capacitor build android --prod && cp -r src/assets/audio/* ./android/app/src/main/res/raw && cp AndroidManifest.xml ./android/app/src/main && cp file_paths.xml ./android/app/src/main/res/xml/file_paths.xml", "build-android-server": "mkdir ./android/app/src/main/res/raw && cp -r src/assets/audio/* ./android/app/src/main/res/raw && cp AndroidManifest.xml ./android/app/src/main && cp file_paths.xml ./android/app/src/main/res/xml/file_paths.xml", "build-ios": "ionic capacitor build ios --prod && cp info.plist ./ios/App/App/info.plist", "change_app_name": "bash ./automation/changing_name.sh \"${CLIENT_NAME}\" \"${APP_ID}\"", "changing_version": "bash ./automation/changing_version.sh", "changing_target_sdk_version": "bash ./automation/changing_target_sdk_version.sh", "e2e": "ng e2e", "generate_assets": "npx capacitor-assets generate", "i18n:extract": "ngx-translate-extract --input ./src --output ./src/assets/i18n/{en,ar,he}.json --clean --format json", "i18n:init": "ngx-translate-extract --input ./src --output ./src/assets/i18n/template.json --key-as-default-value --replace --format json", "lint": "ng lint", "ng": "ng", "patch": "cd .. && cp -R olivery_clients/* delivery_app/", "prebuild_env": "eval \"echo export const environment = {\\\\\\n production: true,\\\\\\n db: \\'${CLIENT_DB}\\',\\\\\\n url:\\'${CLIENT_URL}\\',\\\\\\n }\\; > ./src/environments/environment.prod.ts\" && eval \"echo export const environment = {\\\\\\n production: false,\\\\\\n db: \\'${CLIENT_DB}\\',\\\\\\n url:\\'${CLIENT_URL}\\',\\\\\\n }\\; > ./src/environments/environment.ts\"", "start": "ng serve", "turn_on_capgo_deployment": "bash ./automation/turning_on_capgo_deployment.sh", "turn_on_capgo_deployment_for_ios": "bash ./automation/turning_on_capgo_deployment_for_ios.sh", "take_images": "bash ./automation/taking_images.sh ${CLIENT_DB} && cp -r ./resources/icon.png ./resources/icon-only.png && cp -r ./resources/login.png ./src/assets/logo.png&& cp -r ./resources/icon.png ./resources/icon-foreground.png && cp -r ./resources/icon.png ./resources/icon-background.png && cp -r ./resources/splash.png ./resources/splash-dark.png && rm -rf ./resources/icon.png", "take_images_for_ios": "bash ./automation/taking_images_for_ios.sh ${CLIENT_DB} && cp -r ./resources/icon.png ./resources/icon-only.png && cp -r ./resources/login.png ./src/assets/logo.png&& cp -r ./resources/icon.png ./resources/icon-foreground.png && cp -r ./resources/icon.png ./resources/icon-background.png && cp -r ./resources/splash.png ./resources/splash-dark.png && rm -rf ./resources/icon.png", "test": "ng test", "background_location_check": "python3 ./automation/background_location_edit.py ${CLIENT_DB}", "watch": "ng build --watch --configuration development"}, "dependencies": {"@angular/animations": "^16.2.12", "@angular/cdk": "^16.0.0", "@angular/common": "^16.0.0", "@angular/compiler": "^16.0.0", "@angular/core": "^16.0.0", "@angular/forms": "^16.0.0", "@angular/google-maps": "^16.2.14", "@angular/material": "^16.1.5", "@angular/platform-browser": "^16.0.0", "@angular/platform-browser-dynamic": "^16.0.0", "@angular/router": "^16.0.0", "@angular/service-worker": "^16.2.10", "@awesome-cordova-plugins/android-permissions": "^6.6.0", "@awesome-cordova-plugins/app-availability": "^6.8.0", "@awesome-cordova-plugins/core": "^6.7.0", "@awesome-cordova-plugins/in-app-browser": "^6.7.0", "@bartholomej/ngx-translate-extract": "^8.0.2", "@capacitor/android": "7.4.2", "@capacitor/app": "^7.0.0", "@capacitor/barcode-scanner": "^2.0.3", "@capacitor/browser": "^7.0.0", "@capacitor/camera": "^7.0.0", "@capacitor/core": "^7.0.0", "@capacitor/device": "^7.0.0", "@capacitor/dialog": "^7.0.0", "@capacitor/filesystem": "^7.0.0", "@capacitor/geolocation": "^7.0.0", "@capacitor/haptics": "^7.0.0", "@capacitor/ios": "^7.0.0", "@capacitor/keyboard": "^7.0.0", "@capacitor/network": "^7.0.0", "@capacitor/share": "^7.0.0", "@capacitor/splash-screen": "^7.0.0", "@capacitor/status-bar": "^7.0.0", "@capawesome-team/capacitor-file-opener": "^7.0.0", "@capgo/capacitor-updater": "^7.0.0", "@capgo/cli": "^5.0.0", "@ionic/angular": "^7.0.0", "@ionic/storage": "^4.0.0", "@ionic/storage-angular": "^4.0.0", "@ngrx/effects": "^16.1.0", "@ngrx/entity": "^16.1.0", "@ngrx/store": "^16.1.0", "@ngrx/store-devtools": "^16.1.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@zxing/library": "^0.21.3", "annyang": "^2.6.1", "barcode-scanner": "file:barcode-scanner", "capacitor-native-settings": "^7.0.0", "chart.js": "^4.4.9", "cordova-plugin-android-permissions": "^1.1.5", "cordova-plugin-system-alert-window-permission": "^0.0.6", "core-js": "^3.36.0", "dynamsoft-camera-enhancer": "^4.1.0", "hammerjs": "^2.0.8", "howler": "^2.2.4", "html5-qrcode": "^2.3.8", "ionicons": "^7.4.0", "merge-stream": "^2.0.0", "moment": "^2.30.0", "ng-circle-progress": "^1.7.1", "ngx-signaturepad": "^0.0.10", "onesignal-cordova-plugin": "^5.2.3", "phonegap-plugin-barcodescanner": "^8.1.0", "rxjs": "~7.8.0", "signature_pad": "^4.1.6", "tslib": "^2.6.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.0.0", "@angular-eslint/builder": "^16.0.0", "@angular-eslint/eslint-plugin": "^16.0.0", "@angular-eslint/eslint-plugin-template": "^16.0.0", "@angular-eslint/schematics": "^16.0.0", "@angular-eslint/template-parser": "^16.0.0", "@angular/cli": "^16.2.11", "@angular/compiler": "^16.0.0", "@angular/compiler-cli": "^16.0.0", "@angular/language-service": "^16.0.0", "@capacitor/assets": "^3.0.5", "@capacitor/cli": "^7.0.0", "@ionic/angular-toolkit": "^9.0.0", "@types/emscripten": "^1.40.1", "@types/howler": "^2.2.11", "@types/jasmine": "~4.3.0", "@types/lodash": "^4.17.6", "@types/node": "^18.19.0", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^7.32.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-jsdoc": "30.7.6", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~4.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "ts-node": "^10.9.0", "typescript": "~5.1.6", "uuid": "^9.0.1"}, "cordova": {"plugins": {"onesignal-cordova-plugin": {}}, "platforms": ["ios", "android"]}}